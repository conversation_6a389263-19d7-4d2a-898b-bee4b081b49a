import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaExternalLinkAlt, FaDownload } from 'react-icons/fa';

const CertificateModal = ({ isOpen, onClose, certificate }) => {
  const [pdfError, setPdfError] = useState(false);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEsc);

    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose]);

  // Reset PDF error state when certificate changes
  useEffect(() => {
    setPdfError(false);
  }, [certificate]);

  // Determine if the certificate is an image or PDF
  const isPdf = certificate?.file?.toLowerCase().endsWith('.pdf');

  // Since we've converted PDFs to JPGs, we'll treat all files as images
  const useImageView = true;

  // Get fallback image for PDF files
  const getFallbackImage = () => {
    // Map of fallback images for different certificate types
    const fallbackMap = {
      'aws': '/certificates/cloud_computing.jpg',
      'cloud': '/certificates/cloud_computing.jpg',
      'python': '/certificates/data_analytics_python.jpg',
      'data': '/certificates/data_analytics_python.jpg',
      'machine learning': '/certificates/data_analytics_python.jpg',
      'ai': '/certificates/data_analytics_python.jpg',
      'cyber': '/certificates/hackerrank_sql.png',
      'security': '/certificates/hackerrank_sql.png',
      'network': '/certificates/cloud_computing.jpg',
      'sql': '/certificates/hackerrank_sql.png',
      'database': '/certificates/hackerrank_sql.png',
    };

    // Find a matching fallback based on certificate title or issuer
    const searchText = (certificate?.title + ' ' + certificate?.issuer).toLowerCase();
    for (const [key, value] of Object.entries(fallbackMap)) {
      if (searchText.includes(key.toLowerCase())) {
        return value;
      }
    }

    // Default fallback
    return '/certificates/cloud_computing.jpg';
  };

  // Handle PDF load error
  const handlePdfError = () => {
    setPdfError(true);
  };

  // Open certificate in new tab
  const openInNewTab = (e) => {
    e.stopPropagation();
    window.open(certificate?.file, '_blank');
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25 }}
            className="relative max-w-5xl w-full max-h-[90vh] bg-dark rounded-lg shadow-2xl overflow-hidden border border-secondary/30"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-secondary/20 bg-dark/80">
              <h3 className="text-xl font-bold text-lightest">{certificate?.title}</h3>
              <button
                onClick={onClose}
                className="p-2 text-light hover:text-secondary rounded-full transition-colors"
              >
                <FaTimes size={20} />
              </button>
            </div>

            {/* Content */}
            <div className="p-4 overflow-auto max-h-[calc(90vh-8rem)] bg-dark/50">
              {/* Display all certificates as images since we've converted PDFs to JPGs */}
              <img
                src={certificate?.file}
                alt={certificate?.title}
                className="w-full h-auto object-contain rounded-md border border-secondary/20"
              />
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-secondary/20 flex justify-between items-center bg-dark/80">
              <div>
                <p className="text-secondary font-medium">{certificate?.issuer}</p>
                <p className="text-light/70 text-sm">{certificate?.date}</p>
              </div>
              <div className="flex space-x-3">
                <a
                  href={certificate?.file}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-outline text-sm py-2 flex items-center"
                  onClick={(e) => e.stopPropagation()}
                >
                  <FaExternalLinkAlt className="mr-2" size={14} />
                  View Full Size
                </a>
                <a
                  href={certificate?.file}
                  download
                  className="btn-primary text-sm py-2 flex items-center"
                  onClick={(e) => e.stopPropagation()}
                >
                  <FaDownload className="mr-2" size={14} />
                  Download
                </a>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CertificateModal;
