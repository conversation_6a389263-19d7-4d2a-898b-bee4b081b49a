<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Image Downloader</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .image-container {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>Project Image Downloader</h1>
    
    <div class="instructions">
        <h2>Instructions:</h2>
        <ol>
            <li>Click the "Download" button below each image</li>
            <li>Save the image to: <code>public/images/projects/</code> with the filename shown</li>
            <li>Make sure to save all images to complete your portfolio</li>
        </ol>
    </div>

    <!-- Sports Portfolio -->
    <div class="image-container">
        <h3>Sports Portfolio (sports-portfolio.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Sports Portfolio">
        <a href="https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="sports-portfolio.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Portfolio Showcase -->
    <div class="image-container">
        <h3>Portfolio Showcase (portfolio-showcase.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1545239351-ef35f43d514b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Portfolio Showcase">
        <a href="https://images.unsplash.com/photo-1545239351-ef35f43d514b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="portfolio-showcase.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Chat Checkpoints -->
    <div class="image-container">
        <h3>Chat Checkpoints (chat-checkpoints.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1611606063065-ee7946f0787a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Chat Checkpoints">
        <a href="https://images.unsplash.com/photo-1611606063065-ee7946f0787a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="chat-checkpoints.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- IndApp -->
    <div class="image-container">
        <h3>IndApp (indapp.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="IndApp">
        <a href="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="indapp.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- GCP Project -->
    <div class="image-container">
        <h3>GCP Project (gcp-project.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="GCP Project">
        <a href="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="gcp-project.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Web Development -->
    <div class="image-container">
        <h3>Web Development (web-development.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Web Development">
        <a href="https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="web-development.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- VCare -->
    <div class="image-container">
        <h3>VCare (vcare.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="VCare">
        <a href="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="vcare.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Village Agency -->
    <div class="image-container">
        <h3>Village Agency (village-agency.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1516937941344-00b4e0337589?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Village Agency">
        <a href="https://images.unsplash.com/photo-1516937941344-00b4e0337589?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="village-agency.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Java Project -->
    <div class="image-container">
        <h3>Java Project (java-project.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Java Project">
        <a href="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="java-project.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- MLOps Fundamentals -->
    <div class="image-container">
        <h3>MLOps Fundamentals (mlops-fundamentals.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="MLOps Fundamentals">
        <a href="https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="mlops-fundamentals.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Java Lab Exercise -->
    <div class="image-container">
        <h3>Java Lab Exercise (java-lab-exercise.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1515879218367-8466d910aaa4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Java Lab Exercise">
        <a href="https://images.unsplash.com/photo-1515879218367-8466d910aaa4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="java-lab-exercise.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Malware Detection with ML -->
    <div class="image-container">
        <h3>Malware Detection with ML (malware-detection.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Malware Detection with ML">
        <a href="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="malware-detection.jpg">
            <button>Download</button>
        </a>
    </div>
</body>
</html>
