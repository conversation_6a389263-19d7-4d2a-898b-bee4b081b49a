import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiAward, FiBriefcase, FiStar } from 'react-icons/fi';
// Note: These components need to be created or imported properly
// import AchievementCard from '../components/ui/AchievementCard';
// import SearchAndFilter from '../components/ui/SearchAndFilter';
// import TabNavigation from '../components/ui/TabNavigation';
// import EmptyState from '../components/ui/EmptyState';
// import DetailModal from '../components/ui/DetailModal';

const AchievementsShowcase = () => {
  const [activeTab, setActiveTab] = useState('certifications');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    category: 'all',
    year: 'all',
    type: 'all'
  });
  const [selectedItem, setSelectedItem] = useState(null);

  // Fixed certifications data
  const certificationsData = [
    {
      id: 1,
      title: "Deep Learning Specialization",
      category: "ai-ml",
      issuer: "DeepLearning.AI",
      year: "2023",
      type: "technical",
      description: "Comprehensive 5-course specialization covering neural networks, deep learning architectures, and their applications in computer vision and natural language processing.",
      skills: ["TensorFlow", "Neural Networks", "Computer Vision", "NLP", "Python", "Keras"],
      credentialURL: "https://coursera.org/verify/specialization/EXAMPLE123"
    },
    {
      id: 2,
      title: "AWS Cloud Practitioner",
      category: "cloud",
      issuer: "Amazon Web Services",
      year: "2023",
      type: "technical",
      description: "Foundational understanding of AWS cloud services, security, architecture, pricing, and support.",
      skills: ["AWS", "Cloud Computing", "DevOps", "Security", "Architecture"],
      credentialURL: "https://aws.amazon.com/verification/EXAMPLE456"
    },
    {
      id: 3,
      title: "Data Structures and Algorithms",
      category: "programming",
      issuer: "NPTEL",
      year: "2022",
      type: "technical",
      description: "Comprehensive course covering fundamental data structures and algorithms.",
      skills: ["Algorithms", "Data Structures", "Problem Solving", "C++", "Java"],
      credentialURL: "https://nptel.ac.in/verify/EXAMPLE789"
    },
    {
      id: 4,
      title: "Microsoft Azure Fundamentals",
      category: "cloud",
      issuer: "Microsoft",
      year: "2023",
      type: "technical",
      description: "Foundational knowledge of cloud services and Microsoft Azure.",
      skills: ["Azure", "Cloud Services", "Microsoft Technologies", "Security", "Compliance"],
      credentialURL: "https://learn.microsoft.com/verify/EXAMPLEABC"
    }
  ];

  const internshipsData = [
    {
      id: 1,
      title: "Software Developer Intern",
      company: "TechSolutions Inc.",
      year: "2023",
      type: "development",
      duration: "3 months",
      description: "Developed and maintained full-stack web applications using modern technologies.",
      skills: ["React", "Node.js", "MongoDB", "REST APIs", "Git", "Agile"]
    },
    {
      id: 2,
      title: "Data Science Intern",
      company: "DataViz Systems",
      year: "2022",
      type: "data-science",
      duration: "4 months",
      description: "Built machine learning models and data visualization dashboards.",
      skills: ["Python", "Pandas", "Scikit-learn", "Matplotlib", "SQL", "Tableau"]
    },
    {
      id: 3,
      title: "Cloud Engineering Intern",
      company: "CloudTech Solutions",
      year: "2022",
      type: "cloud",
      duration: "3 months",
      description: "Assisted in deploying and managing cloud infrastructure on AWS.",
      skills: ["AWS", "Docker", "Jenkins", "Kubernetes", "Linux", "Python"]
    }
  ];

  const achievementsData = [
    {
      id: 1,
      title: "Best Innovation Award",
      event: "Tech Hackathon 2023",
      year: "2023",
      type: "award",
      category: "competition",
      description: "Won first place in a 48-hour hackathon with over 200 participants.",
      skills: ["Machine Learning", "React", "Python", "Innovation", "Team Leadership"]
    },
    {
      id: 2,
      title: "Dean's List Recognition",
      event: "Academic Excellence",
      year: "2023",
      type: "academic",
      category: "academic",
      description: "Achieved Dean's List recognition for maintaining top 5% academic performance.",
      skills: ["Academic Excellence", "Computer Science", "Problem Solving", "Research"]
    },
    {
      id: 3,
      title: "Open Source Contributor",
      event: "GitHub Achievement",
      year: "2022",
      type: "contribution",
      category: "contribution",
      description: "Active contributor to multiple open source projects with over 50 merged pull requests.",
      skills: ["Open Source", "Git", "JavaScript", "Python", "Documentation", "Community"]
    }
  ];

  const tabs = [
    { id: 'certifications', label: 'Certifications', icon: FiAward, count: certificationsData.length },
    { id: 'internships', label: 'Internships', icon: FiBriefcase, count: internshipsData.length },
    { id: 'achievements', label: 'Achievements', icon: FiStar, count: achievementsData.length }
  ];

  const getCurrentData = () => {
    switch (activeTab) {
      case 'certifications': return certificationsData;
      case 'internships': return internshipsData;
      case 'achievements': return achievementsData;
      default: return [];
    }
  };

  const getFilterOptions = () => {
    const data = getCurrentData();
    const categories = [...new Set(data.map(item => item.category || item.company || item.event))];
    const years = [...new Set(data.map(item => item.year))].sort().reverse();
    const types = [...new Set(data.map(item => item.type))];
    return { category: categories, year: years, type: types };
  };

  // Reset filters function
  const resetFilters = () => {
    setSelectedFilters({
      category: 'all',
      year: 'all',
      type: 'all'
    });
    setSearchTerm('');
  };

  const filteredData = useMemo(() => {
    let data = getCurrentData();
    
    // Apply search filter
    if (searchTerm) {
      data = data.filter(item =>
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.skills && item.skills.some(skill => 
          skill.toLowerCase().includes(searchTerm.toLowerCase())
        ))
      );
    }

    // Apply filters
    Object.entries(selectedFilters).forEach(([key, value]) => {
      if (value !== 'all') {
        data = data.filter(item => {
          const itemValue = item[key] || item.company || item.event;
          return itemValue === value;
        });
      }
    });
    
    return data;
  }, [activeTab, searchTerm, selectedFilters]);

  const handleFilterChange = (key, value) => {
    setSelectedFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleItemClick = (item) => {
    setSelectedItem(item);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-primary py-20">
      <div className="section-container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl font-bold text-lightest mb-4">
            <span className="text-secondary font-mono">04.</span> Achievements
          </h1>
          <p className="text-light/70 text-lg max-w-2xl mx-auto">
            A comprehensive showcase of my certifications, internships, and notable achievements
          </p>
        </motion.div>

        {/* Tab Navigation - Need to implement or import */}
        {/* <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          variant="pills"
          className="mb-12"
        /> */}

        {/* Search and Filter - Need to implement or import */}
        {/* <SearchAndFilter
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filters={selectedFilters}
          onFilterChange={handleFilterChange}
          filterOptions={getFilterOptions()}
          onReset={resetFilters}
          className="mb-12"
        /> */}

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-8"
        >
          <p className="text-light/60 text-sm">
            Showing {filteredData.length} of {getCurrentData().length} {activeTab}
          </p>
        </motion.div>

        {/* Content Grid */}
        <AnimatePresence mode="wait">
          {filteredData.length > 0 ? (
            <motion.div
              key={activeTab}
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredData.map((item) => (
                <motion.div
                  key={item.id}
                  variants={itemVariants}
                  layout
                >
                  {/* AchievementCard component needs to be implemented */}
                  <div 
                    className="bg-dark/50 backdrop-blur-sm rounded-xl border border-light/10 p-6 cursor-pointer hover:border-secondary/50 transition-all duration-300"
                    onClick={() => handleItemClick(item)}
                  >
                    <h3 className="text-xl font-bold text-lightest mb-2">{item.title}</h3>
                    <p className="text-secondary mb-3">{item.issuer || item.company || item.event}</p>
                    <p className="text-light/70 text-sm mb-4">{item.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {item.skills?.map((skill, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 bg-secondary/20 text-secondary text-xs rounded-full"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <div className="text-center py-16">
              <h3 className="text-2xl font-bold text-lightest mb-4">No results found</h3>
              <p className="text-light/70 mb-6">
                No {activeTab} match your current search and filter criteria.
              </p>
              <button 
                onClick={resetFilters}
                className="px-6 py-3 bg-secondary text-primary rounded-lg hover:bg-secondary/80 transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          )}
        </AnimatePresence>

        {/* Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <div
                key={tab.id}
                className="text-center p-6 bg-dark/50 backdrop-blur-sm rounded-xl border border-light/10"
              >
                <div className="w-16 h-16 bg-secondary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Icon className="w-8 h-8 text-secondary" />
                </div>
                <h3 className="text-2xl font-bold text-lightest mb-2">{tab.count}</h3>
                <p className="text-light/60 capitalize">{tab.label}</p>
              </div>
            );
          })}
        </motion.div>
      </div>

      {/* Detail Modal - Need to implement or import */}
      {/* <DetailModal
        isOpen={!!selectedItem}
        onClose={() => setSelectedItem(null)}
        item={selectedItem}
        type={activeTab}
      /> */}
    </div>
  );
};

export default AchievementsShowcase;