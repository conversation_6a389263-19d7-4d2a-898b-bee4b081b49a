import { useState } from 'react';
import { motion } from 'framer-motion';

const Experience = () => {
  const [activeTab, setActiveTab] = useState(0);

  const experiences = [
    {
      company: "TechSolutions Inc.",
      position: "Software Developer Intern",
      duration: "May 2022 - Aug 2022",
      description: [
        "Developed and maintained code for client websites using React, Node.js, and MongoDB",
        "Implemented responsive designs and ensured cross-browser compatibility",
        "Collaborated with the design team to create efficient, reusable UI components",
        "Participated in daily stand-ups and sprint planning meetings"
      ],
      technologies: ["React", "JavaScript", "Node.js", "Express", "MongoDB"]
    },
    {
      company: "DataViz Systems",
      position: "Web Development Intern",
      duration: "Jan 2022 - Apr 2022",
      description: [
        "Built interactive data visualization dashboards using React and D3.js",
        "Optimized database queries to improve application performance",
        "Participated in code reviews and implemented feedback from senior developers",
        "Developed and maintained documentation for internal tools"
      ],
      technologies: ["React", "D3.js", "SQL", "Git", "REST APIs"]
    },
    {
      company: "CloudTech Solutions",
      position: "Cloud Engineering Intern",
      duration: "Jun 2021 - Aug 2021",
      description: [
        "Assisted in deploying and managing applications on AWS cloud infrastructure",
        "Configured and maintained CI/CD pipelines using Jenkins",
        "Implemented automated testing procedures to ensure code quality",
        "Collaborated with the DevOps team to optimize cloud resource utilization"
      ],
      technologies: ["AWS", "Docker", "Jenkins", "Linux", "Python"]
    }
  ];

  return (
    <section id="experience" className="py-20 bg-primary">
      <div className="section-container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="section-title">
            <span className="text-secondary font-mono">03.</span> Experience
          </h2>
        </motion.div>

        <div className="mt-10">
          <div className="flex flex-col md:flex-row">
            {/* Tabs */}
            <motion.div
              className="flex md:flex-col overflow-x-auto md:overflow-x-visible mb-6 md:mb-0 md:mr-8 border-b md:border-b-0 md:border-l border-light/20"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              {experiences.map((exp, index) => (
                <button
                  key={index}
                  onClick={() => setActiveTab(index)}
                  className={`px-4 py-3 text-sm font-mono whitespace-nowrap transition-all duration-300 ${
                    activeTab === index
                      ? 'text-secondary border-secondary md:border-l-2 border-b-2 md:border-b-0 bg-secondary/5'
                      : 'text-light hover:text-secondary hover:bg-secondary/5'
                  }`}
                >
                  {exp.company}
                </button>
              ))}
            </motion.div>

            {/* Content */}
            <motion.div
              className="flex-1 min-h-[300px]"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="p-1">
                <h3 className="text-xl font-bold text-lightest">
                  {experiences[activeTab].position}{' '}
                  <span className="text-secondary">@ {experiences[activeTab].company}</span>
                </h3>
                <p className="text-light/70 font-mono text-sm mb-4">
                  {experiences[activeTab].duration}
                </p>
                <ul className="space-y-3">
                  {experiences[activeTab].description.map((item, index) => (
                    <li key={index} className="flex">
                      <span className="text-secondary mr-2 mt-1">▹</span>
                      <span className="text-light/80">{item}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-6">
                  <p className="text-light/70 text-sm mb-2">Technologies used:</p>
                  <div className="flex flex-wrap gap-2">
                    {experiences[activeTab].technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-dark rounded-full text-xs font-mono text-secondary border border-secondary/30"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;
