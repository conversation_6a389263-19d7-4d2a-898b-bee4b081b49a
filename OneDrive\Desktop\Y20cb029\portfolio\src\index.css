@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Fira+Code:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  * {
    @apply border-neutral-200;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply antialiased text-neutral-700 bg-white;
    font-feature-settings: "kern", "liga", "calt", "cv02", "cv03", "cv04", "cv11";
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold tracking-tight text-neutral-900;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }

  p {
    @apply leading-relaxed text-neutral-600;
  }

  a {
    @apply transition-colors duration-200;
  }

  /* Dark mode styles */
  .dark body {
    @apply bg-neutral-950 text-neutral-300;
  }

  .dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
    @apply text-neutral-100;
  }

  .dark p {
    @apply text-neutral-400;
  }
}

/* Professional component styles */
@layer components {
  /* Cards */
  .card {
    @apply bg-white border border-neutral-200 rounded-xl shadow-soft transition-all duration-300;
  }

  .card:hover {
    @apply shadow-medium border-neutral-300;
  }

  .card-interactive {
    @apply card cursor-pointer;
  }

  .card-interactive:hover {
    @apply shadow-large -translate-y-1;
  }

  /* Dark mode cards */
  .dark .card {
    @apply bg-neutral-900 border-neutral-800;
  }

  .dark .card:hover {
    @apply border-neutral-700;
  }

  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-soft;
  }

  .btn-primary:hover {
    @apply shadow-medium;
  }

  .btn-secondary {
    @apply btn bg-neutral-100 text-neutral-900 hover:bg-neutral-200 focus:ring-neutral-500 border border-neutral-300;
  }

  .btn-outline {
    @apply btn border border-neutral-300 text-neutral-700 hover:bg-neutral-50 focus:ring-neutral-500;
  }

  .btn-ghost {
    @apply btn text-neutral-600 hover:bg-neutral-100 hover:text-neutral-900 focus:ring-neutral-500;
  }

  /* Dark mode buttons */
  .dark .btn-secondary {
    @apply bg-neutral-800 text-neutral-100 hover:bg-neutral-700 border-neutral-700;
  }

  .dark .btn-outline {
    @apply border-neutral-700 text-neutral-300 hover:bg-neutral-800;
  }

  .dark .btn-ghost {
    @apply text-neutral-400 hover:bg-neutral-800 hover:text-neutral-100;
  }

  /* Navigation */
  .nav-link {
    @apply text-neutral-600 hover:text-neutral-900 px-3 py-2 text-sm font-medium transition-colors duration-200;
  }

  .nav-link.active {
    @apply text-primary-600;
  }

  .dark .nav-link {
    @apply text-neutral-400 hover:text-neutral-100;
  }

  .dark .nav-link.active {
    @apply text-primary-400;
  }

  /* Forms */
  .form-input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }

  .dark .form-input {
    @apply bg-neutral-800 border-neutral-700 text-neutral-100;
  }

  /* Sections */
  .section {
    @apply py-16 md:py-24;
  }

  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Text styles */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  .text-muted {
    @apply text-neutral-500;
  }

  .dark .text-muted {
    @apply text-neutral-400;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-10px) translateX(5px);
  }
  50% {
    transform: translateY(0) translateX(10px);
  }
  75% {
    transform: translateY(10px) translateX(5px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes glitch {
  0% {
    transform: translate(0);
    text-shadow: 0 0 0 rgba(10, 240, 255, 0);
  }
  2% {
    transform: translate(-2px, 0) skew(0deg);
    text-shadow: -2px 0 2px rgba(10, 240, 255, 0.4);
  }
  4% {
    transform: translate(2px, 0) skew(0deg);
    text-shadow: 2px 0 2px rgba(138, 77, 255, 0.4);
  }
  5% {
    transform: translate(-2px, 0) skew(0deg);
    text-shadow: -2px 0 2px rgba(10, 240, 255, 0.4);
  }
  6% {
    transform: translate(0) skew(5deg);
    text-shadow: 0 0 0 rgba(10, 240, 255, 0);
  }
  7% {
    transform: translate(0) skew(0deg);
    text-shadow: 0 0 0 rgba(10, 240, 255, 0);
  }
  20% {
    transform: translate(0) skew(0deg);
    text-shadow: 0 0 0 rgba(10, 240, 255, 0);
  }
  21% {
    transform: translate(2px, -2px) skew(-5deg);
    text-shadow: 2px 0 2px rgba(138, 77, 255, 0.4);
  }
  22% {
    transform: translate(0) skew(0deg);
    text-shadow: 0 0 0 rgba(10, 240, 255, 0);
  }
  23% {
    transform: translate(-2px, 0) skew(3deg);
    text-shadow: -2px 0 2px rgba(10, 240, 255, 0.4);
  }
  24% {
    transform: translate(0) skew(0deg);
    text-shadow: 0 0 0 rgba(10, 240, 255, 0);
  }
  100% {
    transform: translate(0) skew(0deg);
    text-shadow: 0 0 0 rgba(10, 240, 255, 0);
  }
}

@keyframes neon-glow {
  0%, 100% {
    text-shadow:
      0 0 5px rgba(10, 240, 255, 0.7),
      0 0 10px rgba(10, 240, 255, 0.5),
      0 0 15px rgba(10, 240, 255, 0.3),
      0 0 20px rgba(10, 240, 255, 0.2);
  }
  50% {
    text-shadow:
      0 0 10px rgba(10, 240, 255, 0.9),
      0 0 20px rgba(10, 240, 255, 0.7),
      0 0 30px rgba(10, 240, 255, 0.5),
      0 0 40px rgba(10, 240, 255, 0.3);
  }
}

@keyframes border-flow {
  0%, 100% {
    border-image: linear-gradient(to right, rgba(10, 240, 255, 0.7), rgba(138, 77, 255, 0.7)) 1;
  }
  50% {
    border-image: linear-gradient(to right, rgba(138, 77, 255, 0.7), rgba(10, 240, 255, 0.7)) 1;
  }
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
  50% {
    border-radius: 50% 60% 30% 60% / 40% 30% 70% 50%;
  }
  75% {
    border-radius: 60% 40% 60% 30% / 30% 60% 40% 70%;
  }
}

@keyframes matrix-rain {
  0% {
    transform: translateY(-100%);
    opacity: 1;
  }
  85% {
    opacity: 1;
  }
  100% {
    transform: translateY(1000%);
    opacity: 0;
  }
}

/* Custom classes */
.glass-card {
  @apply backdrop-blur-md bg-white/5 border border-white/10 rounded-lg shadow-md;
}

.glass-card-dark {
  @apply backdrop-blur-md bg-black/20 border border-white/5 rounded-lg shadow-md;
}

.card {
  @apply bg-gray-800/80 border border-gray-700/50 rounded-lg shadow-md;
}

.card-hover {
  @apply transition-all duration-300 hover:border-gray-600/70 hover:shadow-lg;
}

.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500;
}

.text-gradient-alt {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-blue-500;
}

.shimmer {
  background: linear-gradient(90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-rotate {
  animation: rotate 10s linear infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-glitch {
  animation: glitch 5s infinite;
}

.animate-neon {
  animation: neon-glow 2s infinite alternate;
}

.animate-morph {
  animation: morph 8s ease-in-out infinite;
}

.animate-border-flow {
  animation: border-flow 3s infinite;
}

.animate-matrix {
  animation: matrix-rain 3s linear infinite;
}

/* Staggered animation delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.2);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 10px rgba(79, 70, 229, 0.3);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.03);
}

.hover-border {
  @apply border border-gray-700/50 transition-colors duration-300;
}

.hover-border:hover {
  @apply border-gray-600;
}

/* Button styles */
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-300 inline-flex items-center justify-center;
}

.btn-primary {
  @apply bg-indigo-600 hover:bg-indigo-700 text-white;
}

.btn-secondary {
  @apply bg-gray-700 hover:bg-gray-600 text-white;
}

.btn-outline {
  @apply border border-gray-600 hover:border-indigo-500 hover:text-indigo-400 text-gray-300;
}

.btn-ghost {
  @apply text-gray-300 hover:text-white hover:bg-gray-800/50;
}

/* Responsive design utilities */
.responsive-container {
  @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.responsive-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.responsive-flex {
  @apply flex flex-col md:flex-row;
}

/* 3D effects */
.perspective {
  perspective: 1000px;
}

.perspective-stronger {
  perspective: 2000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.translate-z {
  transform: translateZ(50px);
}

.translate-z-sm {
  transform: translateZ(20px);
}

.translate-z-lg {
  transform: translateZ(80px);
}

.rotate-x {
  transform: rotateX(10deg);
}

.rotate-y {
  transform: rotateY(10deg);
}

.rotate-3d {
  transform: rotate3d(1, 1, 1, 10deg);
}

.tilt-card {
  transition: transform 0.5s ease;
  transform-style: preserve-3d;
  will-change: transform;
}

.tilt-card-content {
  transform-style: preserve-3d;
}

.depth-1 {
  transform: translateZ(20px);
}

.depth-2 {
  transform: translateZ(40px);
}

.depth-3 {
  transform: translateZ(60px);
}

.shadow-3d {
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.2),
    0 5px 8px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.shadow-3d-hover {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 10px 15px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Futuristic UI elements */
.cyber-border {
  @apply relative;
  border: 1px solid transparent;
  background: linear-gradient(to right, #0af0ff, #8a4dff) border-box;
  -webkit-mask:
    linear-gradient(#fff 0 0) padding-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.cyber-border-animated {
  @apply relative;
  border: 1px solid transparent;
  background: linear-gradient(90deg, #0af0ff, #8a4dff, #0af0ff) border-box;
  background-size: 200% 100%;
  -webkit-mask:
    linear-gradient(#fff 0 0) padding-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: border-flow 3s linear infinite;
}

.cyber-button {
  @apply relative overflow-hidden px-6 py-3 bg-black/50 text-white border border-blue-500/50 rounded-md transition-all duration-300;
}

.cyber-button::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 transition-opacity duration-300;
}

.cyber-button:hover::before {
  @apply opacity-100;
}

.cyber-button::after {
  content: '';
  @apply absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-purple-500 transform scale-x-0 origin-left transition-transform duration-300;
}

.cyber-button:hover::after {
  @apply scale-x-100;
}

.pro-card {
  @apply relative overflow-hidden backdrop-blur-md bg-white/5 border border-white/10 rounded-xl shadow-xl transition-all duration-500;
  background-image:
    radial-gradient(circle at top right, rgba(10, 240, 255, 0.1), transparent 70%),
    radial-gradient(circle at bottom left, rgba(138, 77, 255, 0.1), transparent 70%);
}

.pro-card::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 transition-opacity duration-500;
}

.pro-card:hover::before {
  @apply opacity-100;
}

.pro-card::after {
  content: '';
  @apply absolute -inset-px bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-blue-500/30 rounded-xl opacity-0 transition-opacity duration-500;
  filter: blur(4px);
}

.pro-card:hover::after {
  @apply opacity-100;
}

.pro-button {
  @apply relative overflow-hidden px-6 py-3 bg-gradient-to-br from-blue-900/80 to-indigo-900/80 text-white rounded-md transition-all duration-300 border border-blue-700/30;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.pro-button:hover {
  @apply bg-gradient-to-br from-blue-800/80 to-indigo-800/80 border-blue-600/50;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pro-button::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 transition-opacity duration-300;
}

.pro-button:hover::before {
  @apply opacity-100;
}

.pro-input {
  @apply w-full px-4 py-3 bg-blue-950/70 border border-blue-800/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white transition-all duration-300;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pro-input:focus {
  @apply border-blue-600/70 bg-blue-900/70;
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 0 0 3px rgba(59, 130, 246, 0.1);
}

.pro-badge {
  @apply px-3 py-1 text-xs font-medium rounded-full transition-all duration-300;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.pro-badge:hover {
  background: linear-gradient(to right, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
  border: 1px solid rgba(59, 130, 246, 0.5);
}

.pro-section {
  @apply relative rounded-xl overflow-hidden;
  background:
    linear-gradient(to bottom right, rgba(30, 58, 138, 0.1), rgba(30, 58, 138, 0.05)),
    linear-gradient(to top right, rgba(109, 40, 217, 0.1), rgba(30, 58, 138, 0));
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

:root {
  --color-primary: #030b1c;
  --color-secondary: #0af0ff;
  --color-accent: #8a4dff;
  --color-tertiary: #00c896;
  --color-dark: #020611;
  --color-light: #b8c0d9;
  --color-lightest: #f8fafc;
  --color-highlight: #ff3e7f;
}

:root {
  color-scheme: dark;
}

html {
  scroll-behavior: smooth;
}

body {
  @apply bg-primary text-light font-sans;
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-image:
    radial-gradient(circle at 15% 15%, rgba(10, 240, 255, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 85% 85%, rgba(138, 77, 255, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(0, 200, 150, 0.05) 0%, transparent 70%);
  position: relative;
  overflow-x: hidden;
}

/* Animated background elements */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%238a4dff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(rgba(8, 20, 50, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(8, 20, 50, 0.1) 1px, transparent 1px);
  background-size: auto, 40px 40px, 40px 40px;
  pointer-events: none;
  z-index: -1;
  animation: backgroundShift 120s linear infinite;
}

/* Floating particles effect */
body::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 50% 10%, rgba(10, 240, 255, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 80% 20%, rgba(138, 77, 255, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 10% 40%, rgba(0, 200, 150, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 70% 60%, rgba(10, 240, 255, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 30% 80%, rgba(138, 77, 255, 0.05) 0%, transparent 15%);
  filter: blur(20px);
  pointer-events: none;
  z-index: -1;
  animation: particleFloat 30s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
  0% {
    background-position: 0% 0%, 0 0, 0 0;
  }
  100% {
    background-position: 100% 100%, 40px 40px, 40px 40px;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-10px) translateX(10px);
  }
  100% {
    transform: translateY(-20px) translateX(-10px);
  }
}

/* Advanced text animations */
@keyframes textGlitch {
  0% {
    text-shadow: 0.05em 0 0 rgba(255, 0, 0, 0.75),
                -0.05em -0.025em 0 rgba(0, 255, 0, 0.75),
                -0.025em 0.05em 0 rgba(0, 0, 255, 0.75);
  }
  14% {
    text-shadow: 0.05em 0 0 rgba(255, 0, 0, 0.75),
                -0.05em -0.025em 0 rgba(0, 255, 0, 0.75),
                -0.025em 0.05em 0 rgba(0, 0, 255, 0.75);
  }
  15% {
    text-shadow: -0.05em -0.025em 0 rgba(255, 0, 0, 0.75),
                0.025em 0.025em 0 rgba(0, 255, 0, 0.75),
                -0.05em -0.05em 0 rgba(0, 0, 255, 0.75);
  }
  49% {
    text-shadow: -0.05em -0.025em 0 rgba(255, 0, 0, 0.75),
                0.025em 0.025em 0 rgba(0, 255, 0, 0.75),
                -0.05em -0.05em 0 rgba(0, 0, 255, 0.75);
  }
  50% {
    text-shadow: 0.025em 0.05em 0 rgba(255, 0, 0, 0.75),
                0.05em 0 0 rgba(0, 255, 0, 0.75),
                0 -0.05em 0 rgba(0, 0, 255, 0.75);
  }
  99% {
    text-shadow: 0.025em 0.05em 0 rgba(255, 0, 0, 0.75),
                0.05em 0 0 rgba(0, 255, 0, 0.75),
                0 -0.05em 0 rgba(0, 0, 255, 0.75);
  }
  100% {
    text-shadow: -0.025em 0 0 rgba(255, 0, 0, 0.75),
                -0.025em -0.025em 0 rgba(0, 255, 0, 0.75),
                -0.025em -0.05em 0 rgba(0, 0, 255, 0.75);
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes borderGlow {
  0% {
    box-shadow: 0 0 5px rgba(10, 240, 255, 0.5), 0 0 10px rgba(10, 240, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(10, 240, 255, 0.8), 0 0 30px rgba(10, 240, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 5px rgba(10, 240, 255, 0.5), 0 0 10px rgba(10, 240, 255, 0.3);
  }
}

@layer components {
  /* Section layouts */
  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative;
  }

  .section-title {
    @apply font-futuristic text-3xl md:text-4xl font-bold mb-10 text-lightest relative inline-block;
    text-shadow: 0 0 10px rgba(10, 240, 255, 0.3);
  }

  .section-title::before {
    content: "<";
    @apply absolute -left-6 text-secondary opacity-80;
  }

  .section-title::after {
    content: "/>";
    @apply absolute -bottom-2 -right-10 text-secondary opacity-80;
  }

  /* Glass card effect */
  .glass-card {
    @apply bg-glass backdrop-blur-md border border-secondary/20 rounded-lg p-6 shadow-lg transition-all duration-300;
    box-shadow: 0 0 20px rgba(10, 240, 255, 0.1);
  }

  .glass-card:hover {
    @apply border-secondary/40;
    box-shadow: 0 0 30px rgba(10, 240, 255, 0.2);
  }

  /* Navigation */
  .nav-link {
    @apply text-light hover:text-secondary transition-all duration-300 relative overflow-hidden;
  }

  .nav-link::after {
    content: "";
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-secondary transition-all duration-300;
    box-shadow: 0 0 8px rgba(10, 240, 255, 0.8);
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  /* Buttons */
  .btn-primary {
    @apply px-6 py-3 border border-secondary text-secondary rounded-md hover:bg-secondary/10 transition-all duration-300 font-mono text-sm relative overflow-hidden;
    box-shadow: 0 0 15px rgba(10, 240, 255, 0.3), inset 0 0 5px rgba(10, 240, 255, 0.1);
  }

  .btn-primary:hover {
    box-shadow: 0 0 25px rgba(10, 240, 255, 0.5), inset 0 0 10px rgba(10, 240, 255, 0.2);
  }

  .btn-primary::before {
    content: "";
    @apply absolute top-0 left-0 w-full h-full bg-secondary/20 transform -translate-x-full skew-x-12 transition-transform duration-700;
  }

  .btn-primary:hover::before {
    @apply transform translate-x-0;
  }

  .btn-secondary {
    @apply px-6 py-3 border border-accent text-accent rounded-md hover:bg-accent/10 transition-all duration-300 font-mono text-sm relative overflow-hidden;
    box-shadow: 0 0 15px rgba(138, 77, 255, 0.3), inset 0 0 5px rgba(138, 77, 255, 0.1);
  }

  .btn-secondary:hover {
    box-shadow: 0 0 25px rgba(138, 77, 255, 0.5), inset 0 0 10px rgba(138, 77, 255, 0.2);
  }

  .btn-outline {
    @apply px-5 py-2 border border-light/30 text-light rounded-md hover:border-secondary hover:text-secondary transition-all duration-300 font-mono text-sm;
  }

  /* Code elements */
  .code-block {
    @apply font-mono text-sm bg-dark/80 p-4 rounded-md border border-secondary/30 backdrop-blur-sm;
    box-shadow: 0 0 15px rgba(10, 240, 255, 0.1), inset 0 0 5px rgba(10, 240, 255, 0.05);
  }

  .terminal-text {
    @apply font-mono text-secondary;
  }

  .terminal-prompt::before {
    content: "> ";
    @apply text-accent;
  }

  /* Cards and containers */
  .tech-card {
    @apply bg-dark/80 p-6 rounded-lg border border-secondary/20 backdrop-blur-sm transition-all duration-300 hover:border-secondary/50;
    box-shadow: 0 0 15px rgba(10, 240, 255, 0.1);
  }

  .tech-card:hover {
    box-shadow: 0 0 25px rgba(10, 240, 255, 0.2);
    transform: translateY(-5px);
  }

  /* Text effects */
  .glow-text {
    @apply text-secondary;
    text-shadow: 0 0 10px rgba(10, 240, 255, 0.8), 0 0 20px rgba(10, 240, 255, 0.4);
  }

  .accent-glow-text {
    @apply text-accent;
    text-shadow: 0 0 10px rgba(138, 77, 255, 0.8), 0 0 20px rgba(138, 77, 255, 0.4);
  }

  .highlight-glow-text {
    @apply text-highlight;
    text-shadow: 0 0 10px rgba(255, 62, 127, 0.8), 0 0 20px rgba(255, 62, 127, 0.4);
  }

  .typing-cursor::after {
    content: "|";
    @apply text-secondary animate-pulse;
  }

  /* Background patterns */
  .grid-pattern {
    background-image:
      linear-gradient(rgba(138, 77, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(138, 77, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .cyber-grid {
    @apply bg-cyber-grid bg-cyber-grid;
  }

  /* Scrollbar styling */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-dark/50 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-secondary/30 rounded-full hover:bg-secondary/50 transition-colors duration-300;
  }

  /* Advanced text effects */
  .text-glitch {
    animation: textGlitch 2.5s infinite;
    position: relative;
  }

  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-secondary via-accent to-tertiary;
    background-size: 200% auto;
    animation: gradientFlow 3s ease infinite;
  }

  .text-glow-intense {
    @apply text-secondary;
    text-shadow: 0 0 10px rgba(10, 240, 255, 0.8),
                 0 0 20px rgba(10, 240, 255, 0.6),
                 0 0 30px rgba(10, 240, 255, 0.4),
                 0 0 40px rgba(10, 240, 255, 0.2);
  }

  /* 3D effects */
  .card-3d {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  .card-3d-content {
    transform: translateZ(20px);
    transform-style: preserve-3d;
  }

  /* Advanced borders */
  .border-glow {
    animation: borderGlow 2s infinite;
  }

  .border-gradient {
    border: 2px solid;
    border-image: linear-gradient(to right, var(--color-secondary), var(--color-accent)) 1;
  }

  /* Advanced backgrounds */
  .bg-grid-animated {
    background-size: 50px 50px;
    background-image:
      linear-gradient(to right, rgba(10, 240, 255, 0.1) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(10, 240, 255, 0.1) 1px, transparent 1px);
    animation: backgroundShift 20s linear infinite;
  }

  .bg-gradient-animated {
    background: linear-gradient(-45deg, #030b1c, #0a1631, #061b3c, #0a2450);
    background-size: 400% 400%;
    animation: gradientFlow 15s ease infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px);
  }

  .hover-glow {
    transition: box-shadow 0.3s ease, transform 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 15px rgba(10, 240, 255, 0.5), 0 0 30px rgba(10, 240, 255, 0.3);
    transform: translateY(-5px) scale(1.02);
  }

  /* Background size utility */
  .bg-size-200 {
    background-size: 200% auto;
  }
}
