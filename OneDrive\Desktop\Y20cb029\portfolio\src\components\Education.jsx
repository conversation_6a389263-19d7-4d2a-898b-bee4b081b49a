import { motion } from 'framer-motion';
import { FaGraduationCap, FaMedal, FaUniversity } from 'react-icons/fa';

const Education = () => {
  const education = [
    {
      degree: "Bachelor of Technology",
      field: "Computer Science and Business Systems",
      institution: "Vellore Institute of Technology",
      location: "Vellore, India",
      duration: "2019 - 2023",
      gpa: "8.7/10",
      icon: <FaGraduationCap />,
      details: [
        "Specialized in software development, data structures, algorithms, and business systems integration",
        "Completed capstone project on 'AI-Powered Customer Support System'",
        "Active member of the Coding Club and participated in multiple hackathons"
      ]
    },
    {
      degree: "Higher Secondary Education",
      field: "Science (PCM with Computer Science)",
      institution: "Delhi Public School",
      location: "Hyderabad, India",
      duration: "2017 - 2019",
      gpa: "92%",
      icon: <FaUniversity />,
      details: [
        "Focused on Physics, Chemistry, Mathematics, and Computer Science",
        "Participated in various science competitions and coding challenges",
        "Led the school's technology club and organized tech workshops"
      ]
    }
  ];

  const achievements = [
    "Dean's List for academic excellence (2020-2023)",
    "2nd place in VIT Hackathon (2022)",
    "Best Project Award for 'Smart Inventory Management System' (2023)",
    "Merit Scholarship recipient (2019-2023)",
    "Published paper on 'Efficient Database Management in Cloud Systems' (2022)"
  ];

  return (
    <section id="education" className="py-20 bg-dark">
      <div className="section-container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="section-title">
            <span className="text-secondary font-mono">04.</span> Education
          </h2>
        </motion.div>

        <div className="mt-10">
          <div className="space-y-12">
            {education.map((edu, index) => (
              <motion.div
                key={index}
                className="relative pl-8 md:pl-0"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="md:grid md:grid-cols-12 gap-4 items-start">
                  {/* Timeline for mobile */}
                  <div className="absolute left-0 top-0 md:hidden">
                    <div className="h-full w-0.5 bg-secondary/30 absolute left-1.5 top-8"></div>
                    <div className="w-4 h-4 rounded-full bg-secondary text-primary flex items-center justify-center">
                      {edu.icon}
                    </div>
                  </div>

                  {/* Left column with date */}
                  <div className="md:col-span-3 mb-2 md:mb-0 hidden md:block">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-secondary text-primary flex items-center justify-center mr-3">
                        {edu.icon}
                      </div>
                      <span className="text-light/70 font-mono">{edu.duration}</span>
                    </div>
                  </div>

                  {/* Right column with content */}
                  <div className="md:col-span-9 bg-primary p-6 rounded-lg shadow-lg">
                    <div className="md:hidden text-light/70 font-mono mb-2">{edu.duration}</div>
                    <h3 className="text-xl font-bold text-lightest">{edu.degree} in {edu.field}</h3>
                    <p className="text-secondary mb-2">{edu.institution}, {edu.location}</p>
                    <p className="text-light/70 mb-4">GPA: {edu.gpa}</p>

                    <ul className="space-y-2">
                      {edu.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex">
                          <span className="text-secondary mr-2">▹</span>
                          <span className="text-light/80">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Achievements */}
          <motion.div
            className="mt-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h3 className="text-2xl font-bold text-lightest mb-6 flex items-center">
              <FaMedal className="text-secondary mr-3" /> Achievements & Awards
            </h3>

            <div className="grid md:grid-cols-2 gap-4">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={index}
                  className="bg-primary p-4 rounded-lg flex items-start"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="text-secondary mr-3 mt-1">▹</div>
                  <div className="text-light/80">{achievement}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Education;
