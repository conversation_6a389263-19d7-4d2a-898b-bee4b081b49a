import { motion, AnimatePresence } from 'framer-motion';
import { FiX, FiExternalLink, FiCalendar, FiMapPin, FiAward, FiUser, FiStar } from 'react-icons/fi';

const DetailModal = ({ item, isOpen, onClose, type }) => {
  if (!item) return null;

  const getTypeIcon = () => {
    switch (type) {
      case 'certifications':
        return FiAward;
      case 'internships':
        return FiUser;
      case 'achievements':
        return FiStar;
      default:
        return FiAward;
    }
  };

  const getTypeColor = () => {
    switch (type) {
      case 'certifications':
        return 'blue';
      case 'internships':
        return 'green';
      case 'achievements':
        return 'purple';
      default:
        return 'blue';
    }
  };

  const TypeIcon = getTypeIcon();
  const color = getTypeColor();

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm"
          onClick={handleBackdropClick}
          onKeyDown={handleKeyDown}
          tabIndex={-1}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, type: "spring", stiffness: 300, damping: 30 }}
            className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="relative">
              <div className="h-64 overflow-hidden">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = `/images/placeholder-${type}.jpg`;
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              </div>
              
              {/* Close Button */}
              <button
                onClick={onClose}
                className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300"
                aria-label="Close modal"
              >
                <FiX className="w-5 h-5" />
              </button>

              {/* Type Icon */}
              <div className={`absolute top-4 left-4 w-12 h-12 bg-${color}-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-${color}-500/30`}>
                <TypeIcon className={`w-6 h-6 text-${color}-400`} />
              </div>

              {/* Year Badge */}
              <div className={`absolute top-4 left-20 bg-${color}-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg`}>
                {item.year}
              </div>
            </div>

            {/* Content */}
            <div className="p-8 overflow-y-auto max-h-[calc(90vh-16rem)]">
              {/* Title */}
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {item.title}
              </h2>
