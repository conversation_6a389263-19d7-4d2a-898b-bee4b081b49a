import { useState, useRef } from 'react';
import { motion, useInView, useScroll, useTransform } from 'framer-motion';
import { FaCertificate, FaExternalLinkAlt, FaAws, FaPython, FaShieldAlt, FaCloud, FaDatabase, FaCode, FaNetworkWired, FaLaptopCode, FaRobot, FaSearch, FaFilter, FaGraduationCap } from 'react-icons/fa';
import CertificateModal from './CertificateModal';

// Import custom components
import Card3D from './ui/Card3D';
import AnimatedText from './ui/AnimatedText';
import { useStaggeredAnimation } from '../hooks/useAnimations';

const Certifications = () => {
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');

  // Refs for animations and scroll effects
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: false, margin: "-100px" });

  // Refs for scroll animations
  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  // Parallax effect values
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const y3 = useTransform(scrollYProgress, [0, 1], [0, -150]);

  // Use custom staggered animation hook
  const { ref: certsRef, isInView: certsInView, containerVariants, itemVariants } = useStaggeredAnimation(0.05);

  const openModal = (certificate) => {
    setSelectedCertificate(certificate);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Enhanced certificate categories with icons and colors
  const categories = [
    { id: 'all', name: 'All', icon: <FaCertificate />, color: 'secondary' },
    { id: 'cloud', name: 'Cloud', icon: <FaCloud />, color: 'secondary' },
    { id: 'ai', name: 'AI & ML', icon: <FaRobot />, color: 'accent' },
    { id: 'security', name: 'Security', icon: <FaShieldAlt />, color: 'tertiary' },
    { id: 'programming', name: 'Programming', icon: <FaCode />, color: 'secondary' },
    { id: 'data', name: 'Data', icon: <FaDatabase />, color: 'accent' },
  ];

  // Certificate data
  const certifications = [
    {
      title: "AWS Academy Cloud Architecting",
      issuer: "AWS Academy",
      date: "June 2023",
      description: "Comprehensive training on designing and implementing distributed systems on AWS, including security, scalability, and reliability best practices.",
      file: "/certificates/aws_cloud_architecting.jpg",
      icon: <FaAws />,
      category: "cloud"
    },
    {
      title: "AWS Machine Learning Foundations",
      issuer: "AWS Academy",
      date: "March 2023",
      description: "Foundational course covering machine learning concepts, algorithms, and implementation on AWS cloud services.",
      file: "/certificates/aws_machine_learning.jpg",
      icon: <FaRobot />,
      category: "ai"
    },
    {
      title: "AI & Machine Learning Certification",
      issuer: "AICTE",
      date: "March 2023",
      description: "Comprehensive certification in artificial intelligence and machine learning concepts, algorithms, and practical applications.",
      file: "/certificates/aiml_certification.jpg",
      icon: <FaRobot />,
      category: "ai"
    },
    {
      title: "Python Essentials",
      issuer: "Cisco Networking Academy",
      date: "January 2023",
      description: "Advanced Python programming course covering object-oriented programming, modules, packages, and file operations.",
      file: "/certificates/python_essentials.jpg",
      icon: <FaPython />,
      category: "programming"
    },
    {
      title: "Introduction to Cybersecurity",
      issuer: "Cisco Networking Academy",
      date: "January 2023",
      description: "Foundational course on cybersecurity principles, threats, vulnerabilities, and basic security measures.",
      file: "/certificates/cybersecurity_intro.jpg",
      icon: <FaShieldAlt />,
      category: "security"
    },
    {
      title: "Juniper Networks Internship",
      issuer: "Juniper Networks",
      date: "August 2023",
      description: "Internship focused on network engineering, security, and automation using Juniper Networks technologies.",
      file: "/certificates/juniper_internship.jpg",
      icon: <FaNetworkWired />,
      category: "security"
    },
    {
      title: "Palo Alto Networks Certification",
      issuer: "Palo Alto Networks",
      date: "November 2023",
      description: "Certification in network security fundamentals using Palo Alto Networks technologies and best practices.",
      file: "/certificates/paloalto_certification.jpg",
      icon: <FaShieldAlt />,
      category: "security"
    },
    {
      title: "HackerRank SQL Certificate",
      issuer: "HackerRank",
      date: "August 2022",
      description: "Certification validating proficiency in SQL queries, database manipulation, and data analysis.",
      file: "/certificates/hackerrank_sql.png",
      icon: <FaDatabase />,
      category: "data"
    },
    {
      title: "Data Analytics with Python",
      issuer: "NPTEL",
      date: "June 2023",
      description: "Comprehensive course on data analysis techniques using Python libraries like Pandas, NumPy, and Matplotlib.",
      file: "/certificates/data_analytics_python.jpg",
      icon: <FaLaptopCode />,
      category: "data"
    },
    {
      title: "Cloud Computing and Distributed Systems",
      issuer: "NPTEL",
      date: "June 2023",
      description: "In-depth course on cloud computing architectures, distributed systems, and implementation strategies.",
      file: "/certificates/cloud_computing.jpg",
      icon: <FaCloud />,
      category: "cloud"
    }
  ];

  // Filter certificates based on search term and active filter
  const filteredCertifications = certifications.filter(cert => {
    const matchesSearch = cert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.issuer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = activeFilter === 'all' || cert.category === activeFilter;

    return matchesSearch && matchesFilter;
  });

  // We're already using containerVariants and itemVariants from the useStaggeredAnimation hook
  // So we don't need to define them again

  return (
    <section id="certifications" className="py-20 relative overflow-hidden" ref={sectionRef}>
      {/* Background elements with parallax effect */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-20 left-10 w-64 h-64 rounded-full bg-secondary/5 blur-[100px]"
          style={{ y: y1 }}
        />
        <motion.div
          className="absolute bottom-40 right-20 w-80 h-80 rounded-full bg-accent/5 blur-[120px]"
          style={{ y: y2 }}
        />
        <motion.div
          className="absolute top-1/2 left-1/3 w-40 h-40 rounded-full bg-tertiary/5 blur-[80px]"
          style={{ y: y3 }}
        />

        {/* Grid pattern */}
        <div className="absolute inset-0 bg-grid-animated opacity-10"></div>
      </div>

      <div className="section-container relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center text-center mb-12"
        >
          <h2 className="section-title mb-4">
            <span className="text-secondary font-mono">06.</span> Certifications
          </h2>
          <AnimatedText
            text="Professional Achievements & Credentials"
            className="text-xl text-light/80 max-w-2xl"
            type="gradient"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-center max-w-3xl mx-auto mb-10"
        >
          <p className="text-light/70">
            Professional certifications and courses I've completed to enhance my skills and knowledge in various technologies and domains.
            Click on any certificate to view it in full size.
          </p>
        </motion.div>

        {/* Search and filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mb-12"
        >
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            {/* Search bar */}
            <div className="relative w-full md:w-auto flex-grow max-w-md">
              <Card3D
                className="overflow-hidden rounded-full"
                glowColor="rgba(10, 240, 255, 0.5)"
                intensity={3}
                borderGlow={false}
              >
                <div className="relative w-full">
                  <input
                    type="text"
                    placeholder="Search certificates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-dark/80 backdrop-blur-sm border border-secondary/20 rounded-full py-3 pl-12 pr-4 text-light focus:outline-none focus:border-secondary/50 transition-all duration-300"
                  />
                  <motion.div
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-secondary/70"
                    animate={{ scale: searchTerm ? [1, 1.2, 1] : 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <FaSearch />
                  </motion.div>
                </div>
              </Card3D>
            </div>

            {/* Filter buttons */}
            <motion.div
              className="flex flex-wrap gap-3 justify-center md:justify-end"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {categories.map((category, index) => (
                <motion.div
                  key={category.id}
                  variants={itemVariants}
                  whileHover={{
                    y: -5,
                    transition: { type: "spring", stiffness: 300, damping: 10 }
                  }}
                >
                  <Card3D
                    className="overflow-hidden rounded-full"
                    glowColor={`var(--color-${category.color})`}
                    intensity={activeFilter === category.id ? 10 : 5}
                    borderGlow={activeFilter === category.id}
                  >
                    <button
                      onClick={() => setActiveFilter(category.id)}
                      className={`px-4 py-2 rounded-full text-sm font-mono transition-all duration-300 flex items-center gap-2 ${
                        activeFilter === category.id
                          ? `bg-${category.color} text-primary`
                          : `bg-dark/80 backdrop-blur-sm text-light hover:bg-${category.color}/20`
                      }`}
                    >
                      <span className="text-base">{category.icon}</span>
                      {category.name}
                    </button>
                  </Card3D>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </motion.div>

        {/* Certificates grid */}
        <motion.div
          ref={certsRef}
          variants={containerVariants}
          initial="hidden"
          animate={certsInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredCertifications.length > 0 ? (
            filteredCertifications.map((cert, index) => {
              // Find the category object for this certificate
              const certCategory = categories.find(cat => cat.id === cert.category) || categories[0];

              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{
                    y: -10,
                    transition: { type: "spring", stiffness: 300, damping: 10 }
                  }}
                  className="h-full"
                >
                  <Card3D
                    className="h-full"
                    glowColor={`var(--color-${certCategory.color})`}
                    intensity={5}
                  >
                    <div
                      className="bg-dark/80 backdrop-blur-sm rounded-lg border border-light/5 h-full flex flex-col cursor-pointer overflow-hidden"
                      onClick={() => openModal(cert)}
                    >
                      {/* Certificate preview with hover effect */}
                      <div className="relative h-48 overflow-hidden group">
                        {/* Certificate image */}
                        <div className="absolute inset-0 transition-transform duration-700 group-hover:scale-110">
                          <img
                            src={cert.file}
                            alt={cert.title}
                            className="w-full h-full object-cover"
                          />
                        </div>

                        {/* Overlay gradient */}
                        <div className="absolute inset-0 bg-gradient-to-t from-dark/90 via-dark/50 to-transparent"></div>

                        {/* View indicator on hover */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-dark/50">
                          <motion.div
                            className="bg-secondary/20 backdrop-blur-sm p-3 rounded-full border border-secondary/50"
                            whileHover={{ scale: 1.1 }}
                          >
                            <FaExternalLinkAlt className="text-secondary" size={20} />
                          </motion.div>
                        </div>

                        {/* Certificate info */}
                        <div className="absolute bottom-0 left-0 right-0 p-4">
                          <div className="flex items-center">
                            <div className={`w-10 h-10 rounded-full bg-${certCategory.color}/20 flex items-center justify-center mr-3 border border-${certCategory.color}/30 backdrop-blur-sm`}>
                              <span className={`text-${certCategory.color} text-lg`}>
                                {cert.icon || certCategory.icon}
                              </span>
                            </div>
                            <div className="flex-1">
                              <p className={`text-${certCategory.color} font-mono text-xs mb-1`}>{cert.date}</p>
                              <h3 className="text-lightest font-bold text-lg leading-tight">{cert.title}</h3>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Certificate details */}
                      <div className="flex-grow p-5">
                        <p className="text-light/70 text-sm mb-3">{cert.issuer}</p>
                        <p className="text-light/80 text-sm line-clamp-3">{cert.description}</p>
                      </div>

                      {/* View button */}
                      <div className={`border-t border-${certCategory.color}/10 p-4 mt-auto`}>
                        <button
                          className={`flex items-center justify-center text-light hover:text-${certCategory.color} transition-colors duration-300 w-full group`}
                        >
                          <span className="mr-2">View Certificate</span>
                          <motion.div
                            animate={{ x: [0, 5, 0] }}
                            transition={{
                              duration: 1.5,
                              repeat: Infinity,
                              repeatType: "mirror"
                            }}
                          >
                            <FaExternalLinkAlt size={14} />
                          </motion.div>
                        </button>
                      </div>
                    </div>
                  </Card3D>
                </motion.div>
              );
            })
          ) : (
            <motion.div
              variants={itemVariants}
              className="col-span-full text-center py-16"
            >
              <Card3D
                className="inline-block overflow-hidden rounded-lg"
                glowColor="rgba(10, 240, 255, 0.3)"
                intensity={3}
              >
                <div className="bg-dark/80 backdrop-blur-sm p-8 rounded-lg border border-light/10">
                  <motion.div
                    className="text-5xl text-secondary/50 mb-6 mx-auto"
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, 0, -5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity
                    }}
                  >
                    <FaSearch />
                  </motion.div>
                  <h3 className="text-2xl font-bold text-lightest mb-3">No certificates found</h3>
                  <p className="text-light/70 max-w-md mx-auto">
                    Try adjusting your search or filter criteria to find the certificates you're looking for.
                  </p>
                </div>
              </Card3D>
            </motion.div>
          )}
        </motion.div>
      </div>

      {/* Certificate Modal */}
      <CertificateModal
        isOpen={isModalOpen}
        onClose={closeModal}
        certificate={selectedCertificate}
      />
    </section>
  );
};

export default Certifications;
