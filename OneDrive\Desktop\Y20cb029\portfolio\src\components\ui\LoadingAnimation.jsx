import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

const LoadingAnimation = ({ isLoading, onAnimationComplete }) => {
  const [loadingText, setLoadingText] = useState('Initializing system...');
  const [loadingPercentage, setLoadingPercentage] = useState(0);

  // Simulate loading steps
  useEffect(() => {
    if (!isLoading) return;

    const loadingSteps = [
      { text: 'Initializing system...', percentage: 10 },
      { text: 'Loading assets...', percentage: 25 },
      { text: 'Configuring UI components...', percentage: 40 },
      { text: 'Optimizing animations...', percentage: 60 },
      { text: 'Preparing interactive elements...', percentage: 75 },
      { text: 'Finalizing experience...', percentage: 90 },
      { text: 'Ready to launch...', percentage: 100 }
    ];

    let currentStep = 0;

    const interval = setInterval(() => {
      if (currentStep < loadingSteps.length) {
        setLoadingText(loadingSteps[currentStep].text);
        setLoadingPercentage(loadingSteps[currentStep].percentage);
        currentStep++;
      } else {
        clearInterval(interval);
      }
    }, 400);

    return () => clearInterval(interval);
  }, [isLoading]);

  // Logo animation variants
  const logoVariants = {
    hidden: { opacity: 0, scale: 0.8, rotateY: 90 },
    visible: {
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 1.2,
        ease: [0.6, 0.01, -0.05, 0.95]
      }
    },
    exit: {
      opacity: 0,
      scale: 1.2,
      rotateY: -90,
      transition: {
        duration: 0.7,
        ease: "easeInOut"
      }
    }
  };

  // Text animation variants
  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.5,
        duration: 0.8,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.5,
        ease: "easeInOut"
      }
    }
  };

  // Progress bar animation variants
  const progressVariants = {
    hidden: { width: "0%" },
    visible: (custom) => ({
      width: `${custom}%`,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }),
    exit: {
      width: "100%",
      transition: {
        duration: 0.3
      }
    }
  };

  // Container animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5
      }
    },
    exit: {
      opacity: 0,
      transition: {
        delay: 0.2,
        duration: 0.8,
        ease: "easeInOut",
        when: "afterChildren"
      }
    }
  };

  // Binary code animation
  const binaryVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 0.07,
      transition: {
        delay: 0.2,
        duration: 1
      }
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  // Generate binary code
  const generateBinary = () => {
    let binary = '';
    for (let i = 0; i < 1000; i++) {
      binary += Math.random() > 0.5 ? '1' : '0';
      if (i % 8 === 7) binary += ' ';
      if (i % 64 === 63) binary += '\n';
    }
    return binary;
  };

  return (
    <motion.div
      className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-primary overflow-hidden"
      variants={containerVariants}
      initial="hidden"
      animate={isLoading ? "visible" : "exit"}
      onAnimationComplete={() => {
        if (!isLoading) {
          onAnimationComplete?.();
        }
      }}
    >
      {/* Binary code background */}
      <motion.div
        className="absolute inset-0 font-mono text-xs text-secondary/10 overflow-hidden pointer-events-none"
        variants={binaryVariants}
      >
        <pre className="opacity-30">{generateBinary()}</pre>
      </motion.div>

      {/* Grid pattern */}
      <div className="absolute inset-0 bg-grid-animated opacity-5"></div>

      {/* Animated gradient orbs */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-[30rem] h-[30rem] rounded-full bg-secondary/5 blur-[120px]"
        animate={{
          scale: [1, 1.2, 1],
          x: [0, 30, 0],
          y: [0, -30, 0]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />
      <motion.div
        className="absolute bottom-1/4 right-1/4 w-[30rem] h-[30rem] rounded-full bg-accent/5 blur-[120px]"
        animate={{
          scale: [1.2, 1, 1.2],
          x: [0, -30, 0],
          y: [0, 30, 0]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1
        }}
      />

      {/* Logo */}
      <motion.div
        className="relative mb-12"
        variants={logoVariants}
      >
        <div className="w-40 h-40 rounded-full border-4 border-secondary flex items-center justify-center overflow-hidden relative">
          <div className="text-5xl font-futuristic text-secondary">KC</div>

          {/* Rotating border effects */}
          <motion.div
            className="absolute inset-0 rounded-full border-4 border-transparent border-t-accent border-r-secondary"
            animate={{ rotate: 360 }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          ></motion.div>

          <motion.div
            className="absolute inset-[8px] rounded-full border-2 border-transparent border-b-tertiary border-l-secondary"
            animate={{ rotate: -360 }}
            transition={{ duration: 5, repeat: Infinity, ease: "linear" }}
          ></motion.div>

          {/* Pulsing circles */}
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute inset-0 rounded-full border border-secondary/30"
              initial={{ scale: 0.8, opacity: 0.8 }}
              animate={{ scale: 1.5 + (i * 0.2), opacity: 0 }}
              transition={{
                duration: 2 + (i * 0.5),
                repeat: Infinity,
                delay: i * 0.6,
                ease: "easeOut"
              }}
            ></motion.div>
          ))}
        </div>

        {/* Glowing effect */}
        <motion.div
          className="absolute inset-0 rounded-full bg-secondary/20 blur-xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{ duration: 3, repeat: Infinity }}
        ></motion.div>
      </motion.div>

      {/* Loading text */}
      <motion.div
        className="text-center mb-10"
        variants={textVariants}
      >
        <h2 className="text-3xl font-futuristic text-lightest mb-3">
          <motion.span
            className="text-gradient inline-block"
            animate={{
              backgroundPosition: ['0% center', '200% center']
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            Karravula Chandra
          </motion.span>
        </h2>
        <div className="flex items-center justify-center mb-2">
          <div className="h-[1px] w-12 bg-gradient-to-r from-transparent to-secondary/50"></div>
          <p className="text-light/90 font-mono px-4">Portfolio Experience</p>
          <div className="h-[1px] w-12 bg-gradient-to-l from-transparent to-secondary/50"></div>
        </div>
        <p className="text-secondary font-mono text-sm">{loadingText}</p>
      </motion.div>

      {/* Progress bar */}
      <div className="w-80 mb-2">
        <div className="flex justify-between text-xs text-light/50 font-mono mb-2">
          <span>System Boot</span>
          <span>{loadingPercentage}%</span>
        </div>
        <motion.div
          className="w-full h-1 bg-dark/80 rounded-full overflow-hidden backdrop-blur-sm relative"
          variants={textVariants}
        >
          <motion.div
            className="h-full bg-gradient-to-r from-secondary via-accent to-tertiary"
            custom={loadingPercentage}
            variants={progressVariants}
            initial="hidden"
            animate="visible"
          ></motion.div>

          {/* Glow effect on progress bar */}
          <motion.div
            className="absolute top-0 left-0 h-full w-10 bg-white/30 rounded-full blur-sm"
            animate={{ x: ["0%", "100%"] }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatDelay: 0.5
            }}
          ></motion.div>
        </motion.div>
      </div>

      {/* Loading status */}
      <motion.div
        className="font-mono text-xs text-light/60 mt-4"
        variants={textVariants}
      >
        <div className="flex items-center">
          <motion.span
            className="text-secondary mr-2"
            animate={{ opacity: [1, 0.5, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            {'>'}
          </motion.span>
          <motion.span
            animate={{ opacity: [0, 1] }}
            transition={{ duration: 0.2, repeat: Infinity, repeatDelay: 0.8 }}
            className="w-2 h-4 bg-secondary/70 inline-block"
          ></motion.span>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default LoadingAnimation;
