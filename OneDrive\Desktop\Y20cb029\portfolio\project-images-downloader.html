<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project-Specific Images Downloader</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        h1, h2 {
            text-align: center;
            color: #333;
        }
        .instructions {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .image-container {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .image-container h3 {
            margin-top: 0;
            color: #2c5282;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto 15px;
            border-radius: 5px;
        }
        .download-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
            width: 100%;
        }
        .download-btn:hover {
            background-color: #45a049;
        }
        .project-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Project-Specific Images Downloader</h1>
    
    <div class="instructions">
        <h2>Instructions:</h2>
        <ol>
            <li>Click the "Download" button below each image</li>
            <li>Save the image to: <code>public/images/projects/</code> with the filename shown</li>
            <li>These images are specifically chosen to match each of your projects</li>
        </ol>
    </div>

    <!-- Sports Portfolio -->
    <div class="image-container">
        <h3>Sports Portfolio (sports-portfolio.jpg)</h3>
        <p class="project-description">A responsive sports portfolio website showcasing athletic achievements, statistics, and career highlights.</p>
        <img src="https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Sports Portfolio">
        <a href="https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="sports-portfolio.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- Portfolio Showcase -->
    <div class="image-container">
        <h3>Portfolio Showcase (portfolio-showcase.jpg)</h3>
        <p class="project-description">A professional portfolio website template with modern design elements and responsive layouts.</p>
        <img src="https://images.unsplash.com/photo-1507238691740-187a5b1d37b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Portfolio Showcase">
        <a href="https://images.unsplash.com/photo-1507238691740-187a5b1d37b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="portfolio-showcase.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- Chat Checkpoints -->
    <div class="image-container">
        <h3>Chat Checkpoints (chat-checkpoints.jpg)</h3>
        <p class="project-description">A real-time chat application with checkpoint functionality for conversation history.</p>
        <img src="https://images.unsplash.com/photo-1611606063065-ee7946f0787a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Chat Checkpoints">
        <a href="https://images.unsplash.com/photo-1611606063065-ee7946f0787a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="chat-checkpoints.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- IndApp -->
    <div class="image-container">
        <h3>IndApp (indapp.jpg)</h3>
        <p class="project-description">A mobile application connecting independent service providers with customers.</p>
        <img src="https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="IndApp">
        <a href="https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="indapp.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- GCP Project -->
    <div class="image-container">
        <h3>GCP Project (gcp-project.jpg)</h3>
        <p class="project-description">A cloud-based application deployed on Google Cloud Platform with scalable architecture.</p>
        <img src="https://images.unsplash.com/photo-**********-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="GCP Project">
        <a href="https://images.unsplash.com/photo-**********-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="gcp-project.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- Web Development -->
    <div class="image-container">
        <h3>Web Development (web-development.jpg)</h3>
        <p class="project-description">A collection of web development projects showcasing various frontend and backend technologies.</p>
        <img src="https://images.unsplash.com/photo-**********-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Web Development">
        <a href="https://images.unsplash.com/photo-**********-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="web-development.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- VCare -->
    <div class="image-container">
        <h3>VCare (vcare.jpg)</h3>
        <p class="project-description">A healthcare management system for patient care, appointment scheduling, and medical records.</p>
        <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="VCare">
        <a href="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="vcare.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- Village Agency -->
    <div class="image-container">
        <h3>Village Agency (village-agency.jpg)</h3>
        <p class="project-description">A platform connecting rural communities with government services and development programs.</p>
        <img src="https://images.unsplash.com/photo-1516937941344-00b4e0337589?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Village Agency">
        <a href="https://images.unsplash.com/photo-1516937941344-00b4e0337589?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="village-agency.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- Java Project -->
    <div class="image-container">
        <h3>Java Project (java-project.jpg)</h3>
        <p class="project-description">A comprehensive Java application demonstrating OOP principles and design patterns.</p>
        <img src="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Java Project">
        <a href="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="java-project.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- MLOps Fundamentals -->
    <div class="image-container">
        <h3>MLOps Fundamentals (mlops-fundamentals.jpg)</h3>
        <p class="project-description">A project exploring MLOps practices for machine learning model deployment.</p>
        <img src="https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="MLOps Fundamentals">
        <a href="https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="mlops-fundamentals.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- Java Lab Exercise -->
    <div class="image-container">
        <h3>Java Lab Exercise (java-lab-exercise.jpg)</h3>
        <p class="project-description">A collection of Java programming exercises covering fundamental concepts and algorithms.</p>
        <img src="https://images.unsplash.com/photo-1515879218367-8466d910aaa4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Java Lab Exercise">
        <a href="https://images.unsplash.com/photo-1515879218367-8466d910aaa4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="java-lab-exercise.jpg" class="download-btn">
            Download
        </a>
    </div>

    <!-- Malware Detection with ML -->
    <div class="image-container">
        <h3>Malware Detection with ML (malware-detection.jpg)</h3>
        <p class="project-description">An advanced malware detection system using machine learning and deep learning techniques.</p>
        <img src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Malware Detection with ML">
        <a href="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="malware-detection.jpg" class="download-btn">
            Download
        </a>
    </div>
</body>
</html>
