import { motion } from 'framer-motion';
import Hero from '../components/Hero';
import { Link } from 'react-router-dom';
import { FaUserAlt, FaCode, FaBriefcase, FaGraduationCap, FaLaptopCode, FaCertificate, FaEnvelope } from 'react-icons/fa';

const HomePage = () => {
  const sections = [
    { name: 'About', path: '/about', icon: <FaUserAlt /> },
    { name: 'Skills', path: '/skills', icon: <FaCode /> },
    { name: 'Experience', path: '/experience', icon: <FaBriefcase /> },
    { name: 'Education', path: '/education', icon: <FaGraduationCap /> },
    { name: 'Projects', path: '/projects', icon: <FaLaptopCode /> },
    { name: 'Certificates', path: '/certificates', icon: <FaCertificate /> },
    { name: 'Contact', path: '/contact', icon: <FaEnvelope /> },
  ];

  return (
    <div>
      <Hero />
      
      <section className="py-20 bg-dark">
        <div className="section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="section-title text-center">
              <span className="text-secondary font-mono">01.</span> Portfolio Sections
            </h2>
          </motion.div>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-light/70 text-center max-w-3xl mx-auto mb-10"
          >
            Explore different sections of my portfolio to learn more about my skills, experience, and projects.
          </motion.p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
            {sections.map((section, index) => (
              <motion.div
                key={section.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Link to={section.path} className="block">
                  <div className="tech-card h-full p-6 flex flex-col items-center text-center transform hover:-translate-y-2 cursor-pointer">
                    <div className="w-16 h-16 rounded-full bg-dark flex items-center justify-center mb-4 border border-secondary/30">
                      <span className="text-secondary text-2xl">
                        {section.icon}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold text-lightest mb-3">{section.name}</h3>
                    <p className="text-light/70 text-sm">
                      {getDescription(section.name)}
                    </p>
                    
                    <div className="mt-auto pt-4">
                      <span className="btn-primary text-sm py-2 px-4">View {section.name}</span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

// Helper function to get descriptions for each section
const getDescription = (sectionName) => {
  switch (sectionName) {
    case 'About':
      return 'Learn more about my background, interests, and career goals.';
    case 'Skills':
      return 'Explore my technical skills and proficiency levels in various technologies.';
    case 'Experience':
      return 'View my professional experience, internships, and work history.';
    case 'Education':
      return 'Details about my educational background and academic achievements.';
    case 'Projects':
      return 'Browse through my portfolio of projects and applications.';
    case 'Certificates':
      return 'See all my professional certifications and course completions.';
    case 'Contact':
      return 'Get in touch with me for opportunities or collaborations.';
    default:
      return '';
  }
};

export default HomePage;
