// ... existing code ...
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      colors: {
        primary: "#030b1c", // Deeper space blue for more professional look
        secondary: "#0af0ff", // Brighter cyan for better visibility
        accent: "#8a4dff", // Refined purple
        tertiary: "#00c896", // Teal accent for additional highlights
        dark: "#020611", // Even darker background for contrast
        light: "#b8c0d9", // Slightly brighter muted text for better readability
        lightest: "#f8fafc", // Pure bright text
        highlight: "#ff3e7f", // Modern pink highlight
        codeBlue: "#2563eb", // Code syntax highlight
        codeGreen: "#10b981", // Code syntax highlight
        glass: "rgba(3, 11, 28, 0.7)", // For glassmorphism effects
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        mono: ['Fira Code', 'monospace'],
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['Fira Code', 'Consolas', 'monospace'],
        futuristic: ['Orbitron', 'sans-serif'],
        display: ['Space Grotesk', 'sans-serif'],
      },
      boxShadow: {
        'neon': '0 0 5px rgba(10, 240, 255, 0.7), 0 0 20px rgba(10, 240, 255, 0.3)',
        'neon-lg': '0 0 10px rgba(10, 240, 255, 0.7), 0 0 30px rgba(10, 240, 255, 0.3)',
        'neon-accent': '0 0 5px rgba(138, 77, 255, 0.7), 0 0 20px rgba(138, 77, 255, 0.3)',
        'neon-highlight': '0 0 5px rgba(255, 62, 127, 0.7), 0 0 20px rgba(255, 62, 127, 0.3)',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'cyber-grid': 'linear-gradient(rgba(8, 20, 50, 0.2) 1px, transparent 1px), linear-gradient(90deg, rgba(8, 20, 50, 0.2) 1px, transparent 1px)',
      },
      backgroundSize: {
        'cyber-grid': '30px 30px',
      },
      animation: {
        'pulse-slow': 'pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float': 'float 8s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'spin-slow': 'spin 8s linear infinite',
        'bounce-slow': 'bounce 3s infinite',
        'wiggle': 'wiggle 1s ease-in-out infinite',
        'text-shimmer': 'shimmer 2s ease-in-out infinite alternate',
        'border-flow': 'borderFlow 4s ease infinite',
        'scale-pulse': 'scalePulse 3s ease-in-out infinite',
        'slide-in-right': 'slideInRight 0.5s ease-out',
        'slide-in-left': 'slideInLeft 0.5s ease-out',
        'slide-in-up': 'slideInUp 0.5s ease-out',
        'slide-in-down': 'slideInDown 0.5s ease-out',
        'fade-in': 'fadeIn 0.5s ease-out',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        glow: {
          '0%': { textShadow: '0 0 5px rgba(10, 240, 255, 0.5), 0 0 10px rgba(10, 240, 255, 0.3)' },
          '100%': { textShadow: '0 0 10px rgba(10, 240, 255, 0.8), 0 0 20px rgba(10, 240, 255, 0.5), 0 0 30px rgba(10, 240, 255, 0.3)' },
        },
        wiggle: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
        shimmer: {
          '0%': { textShadow: '0 0 5px rgba(10, 240, 255, 0.1), 0 0 10px rgba(10, 240, 255, 0.1)' },
          '100%': { textShadow: '0 0 10px rgba(10, 240, 255, 0.5), 0 0 20px rgba(10, 240, 255, 0.3), 0 0 30px rgba(10, 240, 255, 0.2)' },
        },
        borderFlow: {
          '0%, 100%': {
            borderImage: 'linear-gradient(to right, rgba(10, 240, 255, 0.8), rgba(138, 77, 255, 0.8)) 1',
            borderImageSlice: 1
          },
          '50%': {
            borderImage: 'linear-gradient(to right, rgba(138, 77, 255, 0.8), rgba(10, 240, 255, 0.8)) 1',
            borderImageSlice: 1
          },
        },
        scalePulse: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        slideInUp: {
          '0%': { transform: 'translateY(100%)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        slideInDown: {
          '0%': { transform: 'translateY(-100%)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
      },
    },
  },
  plugins: [],
}
// ... existing code ...
