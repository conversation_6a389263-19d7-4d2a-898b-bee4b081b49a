import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaGith<PERSON>, FaLinkedin, FaTwitter, FaEnvelope, FaCode, FaLaptopCode, FaArrowUp } from 'react-icons/fa';
import { Link } from 'react-scroll';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative bg-dark pt-16 pb-8 overflow-hidden">
      {/* Decorative top border */}
      <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-secondary/50 to-transparent"></div>

      {/* Decorative grid background */}
      <div className="absolute inset-0 cyber-grid opacity-10"></div>

      {/* Animated gradient orbs */}
      <div className="absolute bottom-0 left-1/4 w-64 h-64 rounded-full bg-secondary/5 blur-[80px] animate-pulse-slow"></div>
      <div className="absolute top-0 right-1/4 w-64 h-64 rounded-full bg-accent/5 blur-[80px] animate-pulse-slow" style={{ animationDelay: '2s' }}></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Back to top button */}
        <div className="absolute -top-10 right-10">
          <Link to="hero" smooth={true} duration={800} className="cursor-pointer">
            <motion.div
              className="bg-dark/80 p-3 rounded-full border border-secondary/30 backdrop-blur-sm hover:border-secondary/70 transition-all duration-300 group"
              whileHover={{ y: -5 }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <FaArrowUp className="text-secondary group-hover:animate-bounce" />
              <div className="absolute -inset-1 bg-secondary/10 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
            </motion.div>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {/* Logo and tagline */}
          <motion.div
            className="flex flex-col items-center md:items-start"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Link to="hero" smooth={true} duration={500} className="cursor-pointer">
              <div className="flex items-center space-x-2 group mb-4">
                <div className="relative">
                  <FaCode className="text-secondary text-2xl group-hover:opacity-0 transition-opacity duration-300" />
                  <FaLaptopCode className="text-secondary text-2xl absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <span className="font-futuristic font-bold text-xl text-lightest group-hover:text-secondary transition-colors duration-300">
                  <span className="text-secondary">&lt;</span>
                  Karravula Chandra
                  <span className="text-secondary">/&gt;</span>
                </span>
              </div>
            </Link>
            <p className="text-light/70 text-sm mb-4 text-center md:text-left">
              Software Developer specializing in creating exceptional digital experiences with modern technologies.
            </p>
          </motion.div>

          {/* Quick links */}
          <motion.div
            className="flex flex-col items-center md:items-start"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h3 className="font-futuristic text-lightest mb-4 text-lg">Quick Links</h3>
            <div className="grid grid-cols-2 gap-x-8 gap-y-2">
              <Link to="about" smooth={true} duration={500} className="nav-link text-sm cursor-pointer">About</Link>
              <Link to="skills" smooth={true} duration={500} className="nav-link text-sm cursor-pointer">Skills</Link>
              <Link to="experience" smooth={true} duration={500} className="nav-link text-sm cursor-pointer">Experience</Link>
              <Link to="education" smooth={true} duration={500} className="nav-link text-sm cursor-pointer">Education</Link>
              <Link to="projects" smooth={true} duration={500} className="nav-link text-sm cursor-pointer">Projects</Link>
              <Link to="contact" smooth={true} duration={500} className="nav-link text-sm cursor-pointer">Contact</Link>
            </div>
          </motion.div>

          {/* Contact info */}
          <motion.div
            className="flex flex-col items-center md:items-start"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3 className="font-futuristic text-lightest mb-4 text-lg">Contact</h3>
            <div className="flex flex-col space-y-2 text-sm">
              <a href="mailto:<EMAIL>" className="text-light hover:text-secondary transition-all duration-300 flex items-center">
                <FaEnvelope className="mr-2 text-accent" />
                <EMAIL>
              </a>
              <p className="text-light flex items-center">
                <FaLaptopCode className="mr-2 text-accent" />
                +918186086478
              </p>
            </div>
          </motion.div>
        </div>

        {/* Social links and copyright */}
        <div className="pt-8 border-t border-secondary/10">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <motion.div
              className="flex space-x-6 mb-6 md:mb-0"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <a
                href="https://github.com/karrachandrasekhar"
                target="_blank"
                rel="noopener noreferrer"
                className="text-light hover:text-secondary transition-all duration-300 p-2 border border-transparent hover:border-secondary/30 rounded-full hover:shadow-neon"
                aria-label="GitHub"
              >
                <FaGithub size={18} />
              </a>
              <a
                href="https://linkedin.com/in/karra-chandra-sekhar"
                target="_blank"
                rel="noopener noreferrer"
                className="text-light hover:text-secondary transition-all duration-300 p-2 border border-transparent hover:border-secondary/30 rounded-full hover:shadow-neon"
                aria-label="LinkedIn"
              >
                <FaLinkedin size={18} />
              </a>
              <a
                href="https://twitter.com/karrachandrasekhar"
                target="_blank"
                rel="noopener noreferrer"
                className="text-light hover:text-secondary transition-all duration-300 p-2 border border-transparent hover:border-secondary/30 rounded-full hover:shadow-neon"
                aria-label="Twitter"
              >
                <FaTwitter size={18} />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-light hover:text-secondary transition-all duration-300 p-2 border border-transparent hover:border-secondary/30 rounded-full hover:shadow-neon"
                aria-label="Email"
              >
                <FaEnvelope size={18} />
              </a>
            </motion.div>

            <motion.div
              className="text-center md:text-right"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <p className="text-light/70 text-sm mb-2">
                Designed & Built with <FaHeart className="inline-block text-secondary mx-1" /> by Karravula Chandra
              </p>
              <p className="text-light/50 text-xs font-mono">
                &copy; {currentYear} All Rights Reserved
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
