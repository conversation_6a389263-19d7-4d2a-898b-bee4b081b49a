import { motion } from 'framer-motion';
import { FiAward } from 'react-icons/fi';
import DarkModeToggle from './DarkModeToggle';

const AchievementsHeader = () => {
  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-sm"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <motion.div
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.02 }}
          >
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <FiAward className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                My Achievements
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Professional Portfolio
              </p>
            </div>
          </motion.div>

          {/* Dark Mode Toggle */}
          <DarkModeToggle />
        </div>
      </div>
    </motion.header>
  );
};

export default AchievementsHeader;
