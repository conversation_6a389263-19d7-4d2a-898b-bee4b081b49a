import { motion } from 'framer-motion';
import { useTextRevealAnimation } from '../../hooks/useAnimations';

const AnimatedText = ({
  text,
  className = '',
  once = true,
  delay = 0,
  type = 'reveal', // 'reveal', 'typewriter', 'gradient', 'glow'
  ...props
}) => {
  // Ensure we have text to render
  if (!text) {
    return null;
  }

  const { textVariants, letterVariants } = useTextRevealAnimation();

  // Split text into words and characters for animation
  const words = text.split(' ');

  // Typewriter animation
  if (type === 'typewriter') {
    return (
      <motion.div
        className={`inline-block ${className}`}
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once }}
        transition={{ delay }}
        {...props}
      >
        {words.map((word, wordIndex) => (
          <span key={wordIndex} className="inline-block mr-2">
            {Array.from(word).map((char, charIndex) => (
              <motion.span
                key={charIndex}
                className="inline-block"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once }}
                transition={{
                  delay: delay + (wordIndex * 0.2) + (charIndex * 0.05),
                  duration: 0.2
                }}
              >
                {char}
              </motion.span>
            ))}
          </span>
        ))}
      </motion.div>
    );
  }

  // Gradient animation
  if (type === 'gradient') {
    return (
      <motion.div
        className={`inline-block bg-clip-text text-transparent bg-gradient-to-r from-secondary via-accent to-tertiary bg-size-200 ${className}`}
        initial={{ backgroundPosition: '0% center' }}
        whileInView={{ backgroundPosition: '200% center' }}
        viewport={{ once }}
        transition={{
          duration: 3,
          ease: 'linear',
          repeat: Infinity,
          repeatType: 'reverse'
        }}
        {...props}
      >
        {text}
      </motion.div>
    );
  }

  // Glow animation
  if (type === 'glow') {
    return (
      <motion.div
        className={`inline-block text-secondary ${className}`}
        initial={{ textShadow: '0 0 0px rgba(10, 240, 255, 0)' }}
        whileInView={{
          textShadow: [
            '0 0 5px rgba(10, 240, 255, 0.5), 0 0 10px rgba(10, 240, 255, 0.3)',
            '0 0 15px rgba(10, 240, 255, 0.8), 0 0 30px rgba(10, 240, 255, 0.5)',
            '0 0 5px rgba(10, 240, 255, 0.5), 0 0 10px rgba(10, 240, 255, 0.3)'
          ]
        }}
        viewport={{ once }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatType: 'reverse'
        }}
        {...props}
      >
        {text}
      </motion.div>
    );
  }

  // Default reveal animation
  return (
    <motion.div
      className={`inline-block ${className}`}
      initial="hidden"
      whileInView="visible"
      viewport={{ once }}
      variants={textVariants}
      custom={delay}
      {...props}
    >
      {words.map((word, wordIndex) => (
        <span key={wordIndex} className="inline-block mr-2">
          {Array.from(word).map((char, charIndex) => (
            <motion.span
              key={charIndex}
              className="inline-block"
              variants={letterVariants}
            >
              {char}
            </motion.span>
          ))}
        </span>
      ))}
    </motion.div>
  );
};

export default AnimatedText;
