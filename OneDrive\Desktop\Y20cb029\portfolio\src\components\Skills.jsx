import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import { useRef, useState } from 'react';
import {
  FaHtml5, FaCss3Alt, FaJs, FaReact, FaNodeJs, FaPython,
  FaJava, FaDatabase, FaGitAlt, FaDocker, FaAws, FaFigma,
  FaCode, FaServer, FaTools, FaBrain, FaLaptopCode
} from 'react-icons/fa';
import { SiTailwindcss, SiMongodb, SiExpress, SiTypescript } from 'react-icons/si';

// Import custom components
import Card3D from './ui/Card3D';
import AnimatedText from './ui/AnimatedText';
import SkillCard from './ui/SkillCard';
import { useStaggeredAnimation } from '../hooks/useAnimations';

const Skills = () => {
  // State for featured skill spotlight
  const [featuredSkill, setFeaturedSkill] = useState(null);

  // Refs for scroll animations
  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  // Parallax effect values
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const y3 = useTransform(scrollYProgress, [0, 1], [0, -150]);

  // Use custom staggered animation hook
  const { ref: skillsRef, isInView, containerVariants, itemVariants } = useStaggeredAnimation(0.05);

  // Handle skill hover for spotlight effect
  const handleSkillHover = (isHovered, skill) => {
    if (isHovered) {
      setFeaturedSkill(skill);
    } else {
      // Only clear if this skill was the featured one
      if (featuredSkill && featuredSkill.name === skill?.name) {
        setFeaturedSkill(null);
      }
    }
  };

  // Category icons and colors
  const categoryIcons = {
    frontend: <FaCode className="text-4xl text-secondary" />,
    backend: <FaServer className="text-4xl text-accent" />,
    tools: <FaTools className="text-4xl text-tertiary" />
  };

  // Enhanced skill categories with descriptions and colors
  const skillCategories = [
    {
      title: "Frontend Development",
      icon: categoryIcons.frontend,
      description: "Creating responsive, interactive user interfaces with modern frameworks and libraries.",
      color: "secondary",
      bgGradient: "from-secondary/10 to-transparent",
      skills: [
        {
          name: "HTML5",
          icon: <FaHtml5 className="text-[#E34F26]" />,
          level: 95,
          description: "Semantic markup, accessibility, modern HTML5 features"
        },
        {
          name: "CSS3",
          icon: <FaCss3Alt className="text-[#1572B6]" />,
          level: 90,
          description: "Flexbox, Grid, animations, responsive design"
        },
        {
          name: "JavaScript",
          icon: <FaJs className="text-[#F7DF1E]" />,
          level: 88,
          description: "ES6+, async/await, DOM manipulation, event handling"
        },
        {
          name: "TypeScript",
          icon: <SiTypescript className="text-[#3178C6]" />,
          level: 80,
          description: "Type safety, interfaces, generics, advanced types"
        },
        {
          name: "React",
          icon: <FaReact className="text-[#61DAFB]" />,
          level: 85,
          description: "Hooks, context API, custom hooks, state management"
        },
        {
          name: "Tailwind CSS",
          icon: <SiTailwindcss className="text-[#06B6D4]" />,
          level: 90,
          description: "Utility-first CSS, responsive design, custom configurations"
        },
      ]
    },
    {
      title: "Backend Development",
      icon: categoryIcons.backend,
      description: "Building robust server-side applications, APIs, and database integrations.",
      color: "accent",
      bgGradient: "from-accent/10 to-transparent",
      skills: [
        {
          name: "Node.js",
          icon: <FaNodeJs className="text-[#339933]" />,
          level: 85,
          description: "Event-driven architecture, async programming, middleware"
        },
        {
          name: "Express.js",
          icon: <SiExpress className="text-white" />,
          level: 82,
          description: "RESTful APIs, middleware, routing, authentication"
        },
        {
          name: "Python",
          icon: <FaPython className="text-[#3776AB]" />,
          level: 78,
          description: "Data processing, automation, scripting, Flask/Django"
        },
        {
          name: "Java",
          icon: <FaJava className="text-[#007396]" />,
          level: 75,
          description: "OOP principles, Spring Boot, multithreading"
        },
        {
          name: "MongoDB",
          icon: <SiMongodb className="text-[#47A248]" />,
          level: 80,
          description: "Schema design, aggregation, indexing, Mongoose ODM"
        },
        {
          name: "SQL",
          icon: <FaDatabase className="text-[#4479A1]" />,
          level: 85,
          description: "Query optimization, database design, transactions"
        },
      ]
    },
    {
      title: "Tools & Technologies",
      icon: categoryIcons.tools,
      description: "Leveraging modern tools and platforms to streamline development and deployment.",
      color: "tertiary",
      bgGradient: "from-tertiary/10 to-transparent",
      skills: [
        {
          name: "Git",
          icon: <FaGitAlt className="text-[#F05032]" />,
          level: 90,
          description: "Version control, branching strategies, collaborative workflows"
        },
        {
          name: "Docker",
          icon: <FaDocker className="text-[#2496ED]" />,
          level: 75,
          description: "Containerization, Docker Compose, deployment strategies"
        },
        {
          name: "AWS",
          icon: <FaAws className="text-[#FF9900]" />,
          level: 70,
          description: "EC2, S3, Lambda, CloudFormation, deployment pipelines"
        },
        {
          name: "Figma",
          icon: <FaFigma className="text-[#F24E1E]" />,
          level: 80,
          description: "UI/UX design, prototyping, design systems, collaboration"
        },
      ]
    }
  ];

  return (
    <section id="skills" className="py-20 bg-dark relative overflow-hidden" ref={sectionRef}>
      {/* Background elements with parallax effect */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-20 left-10 w-64 h-64 rounded-full bg-secondary/5 blur-[100px]"
          style={{ y: y1 }}
        />
        <motion.div
          className="absolute bottom-40 right-20 w-80 h-80 rounded-full bg-accent/5 blur-[120px]"
          style={{ y: y2 }}
        />
        <motion.div
          className="absolute top-1/2 left-1/3 w-40 h-40 rounded-full bg-tertiary/5 blur-[80px]"
          style={{ y: y3 }}
        />

        {/* Grid pattern */}
        <div className="absolute inset-0 bg-grid-animated opacity-10"></div>
      </div>

      <div className="section-container relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center text-center mb-12"
        >
          <h2 className="section-title mb-4">
            <span className="text-secondary font-mono">02.</span> Skills
          </h2>
          <AnimatedText
            text="Technical Expertise & Proficiencies"
            className="text-xl text-light/80 max-w-2xl"
            type="gradient"
          />
        </motion.div>

        <div className="mt-10 space-y-24" ref={skillsRef}>
          {skillCategories.map((category, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7, delay: index * 0.2 }}
              className="relative"
            >
              {/* Category header with icon */}
              <div className="flex items-center mb-8 gap-4">
                <Card3D
                  className={`w-16 h-16 rounded-lg flex items-center justify-center bg-dark border border-${category.color}/30`}
                  glowColor={`var(--color-${category.color})`}
                  intensity={10}
                >
                  {category.icon}
                </Card3D>

                <div className="flex-1">
                  <motion.h3
                    className={`text-2xl font-bold text-${category.color} mb-1`}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    {category.title}
                  </motion.h3>

                  <motion.p
                    className="text-light/70 text-sm"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    {category.description}
                  </motion.p>
                </div>
              </div>

              {/* Featured skill spotlight */}
              <AnimatePresence>
                {featuredSkill && (
                  <motion.div
                    className="mb-8 relative"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card3D
                      className={`bg-dark/80 backdrop-blur-md p-8 rounded-lg border border-${category.color}/30`}
                      glowColor={`var(--color-${category.color})`}
                      intensity={8}
                      borderGlow={true}
                    >
                      <div className="flex flex-col md:flex-row gap-6 items-center">
                        {/* Skill icon with animation */}
                        <motion.div
                          className={`text-${category.color} text-6xl p-6 bg-dark/50 rounded-full border border-${category.color}/20`}
                          animate={{
                            rotate: [0, 10, 0, -10, 0],
                            scale: [1, 1.1, 1]
                          }}
                          transition={{ duration: 5, repeat: Infinity }}
                        >
                          {featuredSkill.icon}
                        </motion.div>

                        <div className="flex-1">
                          <div className="flex items-center mb-3">
                            <h3 className={`text-2xl font-bold text-${category.color} mr-3`}>
                              {featuredSkill.name}
                            </h3>
                            <div className={`px-3 py-1 bg-${category.color}/10 rounded-full text-${category.color} text-sm font-mono`}>
                              {featuredSkill.level}% Proficiency
                            </div>
                          </div>

                          <p className="text-light/80 mb-4">
                            {featuredSkill.description}
                          </p>

                          {/* Skill level bar with animated fill */}
                          <div className="relative w-full bg-dark/70 rounded-full h-3 mb-2 overflow-hidden">
                            <motion.div
                              className={`absolute top-0 left-0 h-full bg-gradient-to-r from-${category.color} to-${category.color}/70 rounded-full`}
                              initial={{ width: 0 }}
                              animate={{ width: `${featuredSkill.level}%` }}
                              transition={{ duration: 1, ease: "easeOut" }}
                            />

                            {/* Animated glow effect */}
                            <motion.div
                              className="absolute top-0 left-0 h-full w-20 bg-white/30 rounded-full blur-sm"
                              animate={{ x: ["0%", "100%"] }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                repeatDelay: 0.5
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </Card3D>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Skills grid with enhanced cards */}
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                variants={containerVariants}
                initial="hidden"
                animate={isInView ? "visible" : "hidden"}
              >
                {category.skills.map((skill, skillIndex) => (
                  <SkillCard
                    key={skillIndex}
                    skill={skill}
                    category={category}
                    delay={skillIndex}
                    onHover={handleSkillHover}
                  />
                ))}
              </motion.div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skills;
