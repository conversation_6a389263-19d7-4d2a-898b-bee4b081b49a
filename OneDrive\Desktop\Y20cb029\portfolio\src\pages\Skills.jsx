const Skills = () => {
  // Core skill categories with modern organization
  const skillCategories = [
    {
      title: "Frontend Development",
      icon: "frontend",
      color: "from-blue-500 to-cyan-500",
      skills: [
        { name: "React.js", level: 90, icon: "⚛️" },
        { name: "JavaScript", level: 94, icon: "🟨" },
        { name: "TypeScript", level: 85, icon: "🔷" },
        { name: "HTML5", level: 95, icon: "🧡" },
        { name: "CSS3", level: 92, icon: "💙" },
        { name: "Tailwind CSS", level: 88, icon: "🎨" },
        { name: "Next.js", level: 80, icon: "⚡" },
        { name: "Redux", level: 85, icon: "🔄" }
      ]
    },
    {
      title: "Backend Development",
      icon: "backend",
      color: "from-green-500 to-emerald-500",
      skills: [
        { name: "Node.js", level: 88, icon: "🟢" },
        { name: "Python", level: 95, icon: "🐍" },
        { name: "Express.js", level: 85, icon: "🚀" },
        { name: "Django", level: 82, icon: "🎯" },
        { name: "Flask", level: 85, icon: "🌶️" },
        { name: "RESTful APIs", level: 90, icon: "🔗" },
        { name: "GraphQL", level: 78, icon: "📊" },
        { name: "FastAPI", level: 80, icon: "⚡" }
      ]
    },
    {
      title: "Machine Learning & AI",
      icon: "ai",
      color: "from-purple-500 to-pink-500",
      skills: [
        { name: "TensorFlow", level: 92, icon: "🧠" },
        { name: "PyTorch", level: 88, icon: "🔥" },
        { name: "Scikit-learn", level: 90, icon: "📈" },
        { name: "Computer Vision", level: 85, icon: "👁️" },
        { name: "NLP", level: 82, icon: "💬" },
        { name: "Deep Learning", level: 88, icon: "🤖" },
        { name: "Neural Networks", level: 85, icon: "🧬" },
        { name: "Keras", level: 85, icon: "🎛️" }
      ]
    },
    {
      title: "Database & Cloud",
      icon: "database",
      color: "from-orange-500 to-red-500",
      skills: [
        { name: "MongoDB", level: 88, icon: "🍃" },
        { name: "MySQL", level: 90, icon: "🐬" },
        { name: "PostgreSQL", level: 85, icon: "🐘" },
        { name: "AWS", level: 85, icon: "☁️" },
        { name: "Docker", level: 82, icon: "🐳" },
        { name: "Firebase", level: 82, icon: "🔥" },
        { name: "Redis", level: 78, icon: "🔴" },
        { name: "Git", level: 95, icon: "📝" }
      ]
    }
  ];

  const tools = [
    { name: "VS Code", icon: "💻" },
    { name: "Figma", icon: "🎨" },
    { name: "Postman", icon: "📮" },
    { name: "Jupyter", icon: "📓" },
    { name: "Docker", icon: "🐳" },
    { name: "AWS", icon: "☁️" },
    { name: "Git", icon: "📝" },
    { name: "Tableau", icon: "📊" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-950 dark:to-neutral-900">
        <div className="max-width-content container-padding">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              My <span className="text-gradient bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Skills</span>
            </h1>
            <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
              A comprehensive overview of my technical expertise and the tools I use to bring ideas to life.
            </p>
          </div>

          {/* Skills Overview Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            <div className="card p-6 text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">4+</div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">Years Experience</div>
            </div>
            <div className="card p-6 text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">20+</div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">Technologies</div>
            </div>
            <div className="card p-6 text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">15+</div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">Projects</div>
            </div>
            <div className="card p-6 text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">100%</div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">Passion</div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Skills Section */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-full container-padding">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Technical Expertise
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              My core competencies across different areas of software development and technology.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {skillCategories.map((category, index) => (
              <SkillCategoryCard key={index} category={category} />
            ))}
          </div>
        </div>
      </section>

      {/* Tools & Technologies */}
      <section className="section-padding bg-neutral-50 dark:bg-neutral-900">
        <div className="max-width-content container-padding">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Tools & Technologies
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              The tools and technologies I use to build amazing projects.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
            {tools.map((tool, index) => (
              <div
                key={index}
                className="card-interactive p-6 text-center group"
              >
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  {tool.icon}
                </div>
                <div className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {tool.name}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Soft Skills */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-content container-padding">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Professional Skills
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              Beyond technical skills, I bring strong professional capabilities to every project.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[
              "Problem Solving", "Team Collaboration", "Agile Methodology", "UI/UX Design",
              "Responsive Design", "Testing & Debugging", "Performance Optimization", "Code Review",
              "Technical Writing", "Project Management", "DevOps", "System Architecture"
            ].map((skill, index) => (
              <div
                key={index}
                className="card p-4 text-center hover-lift"
              >
                <div className="font-medium text-neutral-900 dark:text-neutral-100">
                  {skill}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

// Skill Category Card Component
const SkillCategoryCard = ({ category }) => (
  <div className="card-interactive p-8 group">
    {/* Header */}
    <div className="flex items-center mb-8">
      <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${category.color} flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>
        <CategoryIcon icon={category.icon} />
      </div>
      <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
        {category.title}
      </h3>
    </div>

    {/* Skills Grid */}
    <div className="grid grid-cols-2 gap-4">
      {category.skills.map((skill, index) => (
        <SkillItem key={index} skill={skill} />
      ))}
    </div>
  </div>
);

// Individual Skill Item Component
const SkillItem = ({ skill }) => (
  <div className="group">
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center space-x-2">
        <span className="text-lg">{skill.icon}</span>
        <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
          {skill.name}
        </span>
      </div>
      <span className="text-xs font-bold text-primary-600 dark:text-primary-400">
        {skill.level}%
      </span>
    </div>

    {/* Progress Bar */}
    <div className="w-full bg-neutral-200 dark:bg-neutral-800 rounded-full h-2 overflow-hidden">
      <div
        className="h-2 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full transition-all duration-1000 ease-out"
        style={{ width: `${skill.level}%` }}
      >
        <div className="h-full bg-gradient-to-r from-white/20 to-transparent rounded-full"></div>
      </div>
    </div>
  </div>
);

// Category Icon Component
const CategoryIcon = ({ icon }) => {
  const iconMap = {
    frontend: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
      </svg>
    ),
    backend: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2" />
      </svg>
    ),
    ai: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
    database: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
      </svg>
    )
  };

  return iconMap[icon] || iconMap.frontend;
};

export default Skills;
