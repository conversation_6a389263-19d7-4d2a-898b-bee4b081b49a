const Skills = () => {
  // Comprehensive skill categories
  const skillCategories = [
    {
      title: "Machine Learning & AI",
      skills: [
        { name: "TensorFlow", level: 92 },
        { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", level: 88 },
        { name: "Scikit-learn", level: 90 },
        { name: "<PERSON>ras", level: 85 },
        { name: "Computer Vision", level: 85 },
        { name: "Natural Language Processing", level: 82 },
        { name: "Deep Learning", level: 88 },
        { name: "Neural Networks", level: 85 },
        { name: "Reinforcement Learning", level: 75 },
        { name: "Transfer Learning", level: 80 }
      ]
    },
    {
      title: "Data Science",
      skills: [
        { name: "Data Analysis", level: 90 },
        { name: "Data Visualization", level: 88 },
        { name: "Statistical Analysis", level: 85 },
        { name: "Feature Engineering", level: 87 },
        { name: "Pandas", level: 95 },
        { name: "NumPy", level: 92 },
        { name: "Matplotlib", level: 90 },
        { name: "Seaborn", level: 88 },
        { name: "Jupyter Notebook", level: 95 },
        { name: "Data Preprocessing", level: 90 }
      ]
    },
    {
      title: "Frontend Development",
      skills: [
        { name: "HTML5", level: 95 },
        { name: "CSS3", level: 92 },
        { name: "JavaScript", level: 94 },
        { name: "React.js", level: 90 },
        { name: "Redux", level: 85 },
        { name: "Tailwind CSS", level: 88 },
        { name: "Bootstrap", level: 92 },
        { name: "TypeScript", level: 85 },
        { name: "Next.js", level: 80 },
        { name: "Responsive Design", level: 90 }
      ]
    },
    {
      title: "Backend Development",
      skills: [
        { name: "Node.js", level: 88 },
        { name: "Express.js", level: 85 },
        { name: "Python", level: 95 },
        { name: "Django", level: 82 },
        { name: "Flask", level: 85 },
        { name: "FastAPI", level: 80 },
        { name: "RESTful APIs", level: 90 },
        { name: "GraphQL", level: 78 },
        { name: "Authentication", level: 85 },
        { name: "Middleware", level: 82 }
      ]
    },
    {
      title: "Database Technologies",
      skills: [
        { name: "MongoDB", level: 88 },
        { name: "MySQL", level: 90 },
        { name: "PostgreSQL", level: 85 },
        { name: "Firebase", level: 82 },
        { name: "Redis", level: 78 },
        { name: "SQL", level: 90 },
        { name: "NoSQL", level: 85 },
        { name: "Database Design", level: 88 },
        { name: "ORM", level: 85 },
        { name: "Data Modeling", level: 82 }
      ]
    },
    {
      title: "Cloud & DevOps",
      skills: [
        { name: "AWS", level: 85 },
        { name: "Azure", level: 75 },
        { name: "Google Cloud", level: 70 },
        { name: "Docker", level: 82 },
        { name: "Kubernetes", level: 75 },
        { name: "CI/CD", level: 80 },
        { name: "Git & GitHub", level: 95 },
        { name: "Serverless", level: 78 },
        { name: "Microservices", level: 75 },
        { name: "Infrastructure as Code", level: 70 }
      ]
    },
    {
      title: "Tools & Software",
      skills: [
        { name: "VS Code", level: 95 },
        { name: "PyCharm", level: 88 },
        { name: "Jupyter", level: 95 },
        { name: "Postman", level: 90 },
        { name: "Figma", level: 85 },
        { name: "Adobe XD", level: 80 },
        { name: "Tableau", level: 82 },
        { name: "Power BI", level: 80 },
        { name: "Git", level: 95 },
        { name: "JIRA", level: 85 }
      ]
    },
    {
      title: "Programming Languages",
      skills: [
        { name: "Python", level: 95 },
        { name: "JavaScript", level: 92 },
        { name: "TypeScript", level: 85 },
        { name: "Java", level: 75 },
        { name: "C++", level: 70 },
        { name: "SQL", level: 90 },
        { name: "R", level: 75 },
        { name: "Bash", level: 80 },
        { name: "HTML/CSS", level: 95 },
        { name: "PHP", level: 65 }
      ]
    },
    {
      title: "Soft Skills",
      skills: [
        { name: "Problem Solving", level: 95 },
        { name: "Communication", level: 90 },
        { name: "Teamwork", level: 92 },
        { name: "Time Management", level: 88 },
        { name: "Critical Thinking", level: 90 },
        { name: "Adaptability", level: 92 },
        { name: "Leadership", level: 85 },
        { name: "Creativity", level: 88 },
        { name: "Attention to Detail", level: 90 },
        { name: "Project Management", level: 85 }
      ]
    }
  ];

  return (
    <div className="py-10 perspective-stronger">
      {/* Header with 3D effect */}
      <div className="relative mb-16">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl blur-3xl -z-10 transform -rotate-1"></div>
        <div className="relative z-10 text-center">
          <h1 className="text-5xl font-bold mb-4 inline-block relative preserve-3d">
            <span className="absolute -inset-1 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg blur-xl -z-10 transform -rotate-2"></span>
            <span className="text-gradient-alt animate-neon depth-1">My Skills</span>
          </h1>

          <p className="text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed depth-2">
            I've developed a diverse set of technical skills throughout my education and projects.
            Here's an overview of my expertise in various technologies and tools.
          </p>
        </div>
      </div>

      {/* Skills grid with 3D effect */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {skillCategories.map((category, index) => (
          <div
            key={index}
            className="pro-card p-8 rounded-xl shadow-3d hover:shadow-3d-hover transition-all duration-500 transform hover:-rotate-y-3 hover:rotate-x-2 preserve-3d"
          >
            {/* Category header with 3D effect */}
            <div className="flex items-center mb-8 depth-1">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500/10 to-purple-500/10 flex items-center justify-center mr-4">
                <CategoryIcon category={category.title} />
              </div>
              <h2 className="text-2xl font-bold text-gradient-alt">{category.title}</h2>
            </div>

            {/* Skills with 3D effect */}
            <div className="space-y-6 depth-2">
              {category.skills.map((skill, skillIndex) => (
                <div
                  key={skillIndex}
                  className="transform transition-all duration-500 hover:translate-z-sm"
                  style={{ transitionDelay: `${skillIndex * 50}ms` }}
                >
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-200 font-medium">{skill.name}</span>
                    <span className="text-blue-300 font-bold">{skill.level}%</span>
                  </div>
                  <div className="relative">
                    {/* Progress bar background with glow */}
                    <div className="w-full bg-blue-950/50 rounded-full h-3 overflow-hidden backdrop-blur-sm border border-blue-900/50">
                      {/* Progress bar fill with gradient and animation */}
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full relative overflow-hidden"
                        style={{ width: `${skill.level}%` }}
                      >
                        {/* Shimmer effect */}
                        <div className="absolute inset-0 shimmer"></div>
                      </div>
                    </div>

                    {/* Skill level indicator */}
                    <div
                      className="absolute top-0 h-3 flex items-center justify-center"
                      style={{
                        left: `${skill.level}%`,
                        transform: 'translateX(-50%)'
                      }}
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-white shadow-glow"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent"></div>
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-purple-400/20 to-transparent"></div>
            <div className="absolute top-0 left-0 w-4 h-4 border-t border-l border-blue-400/30 rounded-tl"></div>
            <div className="absolute top-0 right-0 w-4 h-4 border-t border-r border-blue-400/30 rounded-tr"></div>
            <div className="absolute bottom-0 left-0 w-4 h-4 border-b border-l border-purple-400/30 rounded-bl"></div>
            <div className="absolute bottom-0 right-0 w-4 h-4 border-b border-r border-purple-400/30 rounded-br"></div>
          </div>
        ))}
      </div>

      {/* Additional skills with 3D effect */}
      <div className="mt-16 pro-section p-8 rounded-xl preserve-3d">
        <h2 className="text-2xl font-bold text-gradient-alt mb-8 depth-1">Additional Skills</h2>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 depth-2">
          {[
            "Problem Solving", "Team Collaboration", "Agile Methodology", "UI/UX Design",
            "Responsive Design", "Testing & Debugging", "Performance Optimization", "Code Review",
            "Technical Writing", "Project Management", "DevOps", "System Architecture"
          ].map((skill, index) => (
            <div
              key={index}
              className="pro-card p-4 rounded-lg text-center text-gray-200 hover-lift shadow-3d hover:shadow-3d-hover transition-all duration-500 transform hover:scale-105"
              style={{ transitionDelay: `${index * 30}ms` }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-lg -z-10"></div>
                <span className="font-medium">{skill}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Helper component for category icons
const CategoryIcon = ({ category }) => {
  switch(category) {
    case "Machine Learning & AI":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      );
    case "Data Science":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      );
    case "Frontend Development":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      );
    case "Backend Development":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
        </svg>
      );
    case "Database Technologies":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
        </svg>
      );
    case "Cloud & DevOps":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
        </svg>
      );
    case "Tools & Software":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      );
    case "Programming Languages":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      );
    case "Soft Skills":
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      );
    default:
      return (
        <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      );
  }
};

export default Skills;
