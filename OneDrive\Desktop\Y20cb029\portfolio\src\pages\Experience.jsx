const Experience = () => {
  // Experience data based on professional background
  const experiences = [
    {
      title: "AI Intern",
      company: "CAP Corporate AI Solutions LLP",
      location: "Chennai, India",
      period: "January 2025 - April 2025",
      description: [
        "Built AI and machine learning models for computer vision tasks such as image classification and object detection using TensorFlow and PyTorch",
        "Developed NLP-based solutions for text analysis and sentiment classification, integrating them into internal tools",
        "Designed data pipelines for processing large image and text datasets using Pandas, NumPy, and OpenCV",
        "Worked closely with AI engineers and software developers to integrate models into production environments",
        "Participated in research reviews, code walkthroughs, and team discussions to improve AI solution quality"
      ],
      technologies: ["Python", "TensorFlow", "PyTorch", "OpenCV", "Scikit-learn", "Pandas", "NumPy"]
    },
    {
      title: "Apprentice",
      company: "Anudip Foundation",
      location: "Hyderabad, India",
      period: "May 2024 - December 2024",
      description: [
        "Underwent intensive training in Core Java, HTML, CSS, JavaScript, and Hibernate, with a strong focus on hands-on practice",
        "Developed a real-time Job Portal project as part of the apprenticeship, implementing key modules like job posting, registration, application tracking, and admin control",
        "Backend functionality was implemented using Java and Hibernate, and the frontend used HTML, CSS, and JavaScript for a responsive interface",
        "Learned software development best practices including debugging, version control with Git, and basic deployment techniques"
      ],
      technologies: ["Java", "Hibernate", "HTML", "CSS", "JavaScript", "MySQL", "Git"]
    },
    {
      title: "IT Recruiting Intern",
      company: "Precisely IT Management Services",
      location: "Hyderabad, India",
      period: "February 2024 - May 2024",
      description: [
        "Performed end-to-end IT recruitment including sourcing, screening, coordinating interviews, and candidate follow-ups",
        "Utilized Boolean search and professional platforms like LinkedIn and Naukri for headhunting technical talent",
        "Maintained candidate databases using ATS (Applicant Tracking Systems) and supported the hiring process for roles like Software Developers and Cloud Engineers",
        "Strengthened understanding of corporate hiring strategies and the technical recruitment life cycle"
      ],
      technologies: ["Boolean Search", "LinkedIn Recruiter", "ATS", "IT Hiring", "Sourcing", "Interview Coordination"]
    },
    {
      title: "Trainee",
      company: "Honeywell",
      location: "Hyderabad, India",
      period: "January 2023 - December 2023",
      description: [
        "Completed training and certification for Microsoft Azure Administrator (AZ-102), gaining expertise in cloud-based solution management",
        "Worked on hands-on labs involving AI solution architecture and implementation using Azure services like Azure ML, Cognitive Services, and Azure Functions",
        "Applied Python for scripting AI models and automating workflows in a cloud environment",
        "Developed practical skills in virtual machine configuration, cloud storage, and monitoring AI deployments"
      ],
      technologies: ["Microsoft Azure", "Python", "Azure ML", "Azure Functions", "Azure Cognitive Services"]
    },
    {
      title: "Python Developer",
      company: "Bytexl Technologies",
      location: "Hyderabad, India",
      period: "June 2022 - December 2022",
      description: [
        "Developed and maintained Python scripts for automation and backend tasks in internal projects",
        "Gained hands-on experience with clean code practices, modular design, and collaborative development using Git",
        "Worked within Agile workflows to deliver features on time and assist in code reviews"
      ],
      technologies: ["Python", "Git", "Agile", "Backend Development", "Automation"]
    }
  ];

  return (
    <div className="py-10">
      <h1 className="text-4xl font-bold mb-8 text-center">
        <span className="text-blue-400">Work</span> Experience
      </h1>

      <p className="text-gray-300 text-center max-w-3xl mx-auto mb-12 leading-relaxed">
        My professional journey includes internships and roles where I've applied my skills
        to real-world projects and gained valuable industry experience.
      </p>

      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-0 md:left-1/2 transform md:-translate-x-1/2 h-full w-1 bg-blue-700"></div>

        {/* Experience items */}
        <div className="space-y-12">
          {experiences.map((exp, index) => (
            <ExperienceItem key={index} experience={exp} isEven={index % 2 === 0} />
          ))}
        </div>
      </div>
    </div>
  );
};

const ExperienceItem = ({ experience, isEven }) => {
  return (
    <div className={`relative flex flex-col md:flex-row ${isEven ? 'md:flex-row-reverse' : ''}`}>
      {/* Timeline dot with pulse effect */}
      <div className="absolute left-0 md:left-1/2 transform md:-translate-x-1/2 w-5 h-5 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 border-4 border-blue-900 z-10">
        <div className="absolute inset-0 rounded-full bg-blue-400 animate-pulse-slow opacity-70"></div>
      </div>

      {/* Content */}
      <div className="md:w-1/2 ml-8 md:ml-0 md:px-8 mb-10">
        <div className="glass-card-dark p-6 rounded-lg shadow-lg hover-lift border border-blue-700/30 hover:border-blue-500/50 transition-all duration-500">
          <h3 className="text-xl font-bold text-gradient-alt mb-1">{experience.title}</h3>
          <h4 className="text-blue-300 font-medium mb-2">{experience.company}</h4>

          <div className="flex justify-between text-sm text-gray-400 mb-4">
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              {experience.location}
            </span>
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {experience.period}
            </span>
          </div>

          <div className="bg-blue-900/30 p-4 rounded-lg border border-blue-800/50 mb-4">
            <ul className="space-y-2 text-gray-300">
              {experience.description.map((item, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-blue-400 mr-2 mt-1">•</span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h5 className="text-sm font-semibold text-gradient mb-2">Technologies:</h5>
            <div className="flex flex-wrap gap-2">
              {experience.technologies.map((tech, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-blue-800/40 backdrop-blur-sm text-xs rounded-full text-blue-200 border border-blue-700/30 hover:border-blue-500/50 transition-all duration-300"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Experience;
