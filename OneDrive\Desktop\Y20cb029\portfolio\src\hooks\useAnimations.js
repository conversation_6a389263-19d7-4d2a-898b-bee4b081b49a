import { useRef, useState } from 'react';

/**
 * Hook for text reveal animations
 * @returns {Object} Animation variants for text reveal
 */
export const useTextRevealAnimation = () => {
  // Text container animation variants
  const textVariants = {
    hidden: {
      opacity: 0
    },
    visible: (i = 1) => ({
      opacity: 1,
      transition: {
        staggerChildren: 0.03,
        delayChildren: 0.04 * i
      }
    })
  };

  // Individual letter animation variants
  const letterVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100
      }
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100
      }
    }
  };

  return { textVariants, letterVariants };
};

/**
 * Hook for magnetic effect on elements
 * @param {number} strength - Strength of the magnetic effect (0-1)
 * @returns {Object} Ref and event handlers for magnetic effect
 */
export const useMagneticEffect = (strength = 0.5) => {
  const ref = useRef(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const handleMouseMove = (e) => {
    if (!ref.current) return;
    
    const { clientX, clientY } = e;
    const { left, top, width, height } = ref.current.getBoundingClientRect();
    
    const x = clientX - (left + width / 2);
    const y = clientY - (top + height / 2);
    
    setPosition({ x: x * strength, y: y * strength });
    
    ref.current.style.transform = `translate(${x * strength}px, ${y * strength}px)`;
  };

  const handleMouseLeave = () => {
    if (!ref.current) return;
    
    setPosition({ x: 0, y: 0 });
    ref.current.style.transform = `translate(0px, 0px)`;
  };

  return { ref, position, handleMouseMove, handleMouseLeave };
};

/**
 * Hook for parallax scrolling effect
 * @param {number} speed - Speed of the parallax effect
 * @returns {Object} Ref and transform style for parallax effect
 */
export const useParallaxEffect = (speed = 0.5) => {
  const ref = useRef(null);
  const [offset, setOffset] = useState(0);

  const handleScroll = () => {
    if (!ref.current) return;
    
    const { top } = ref.current.getBoundingClientRect();
    const offset = window.scrollY;
    const windowHeight = window.innerHeight;
    
    if (top < windowHeight && top > -ref.current.offsetHeight) {
      setOffset(offset * speed);
    }
  };

  return { ref, handleScroll, style: { transform: `translateY(${offset}px)` } };
};
