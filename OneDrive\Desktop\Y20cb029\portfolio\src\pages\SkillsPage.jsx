import Skills from '../components/Skills';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaArrowLeft } from 'react-icons/fa';

const SkillsPage = () => {
  return (
    <div>
      <div className="py-6 bg-dark">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link to="/" className="flex items-center text-light hover:text-secondary transition-colors duration-300">
              <FaArrowLeft className="mr-2" />
              <span>Back to Home</span>
            </Link>
          </motion.div>
        </div>
      </div>
      
      <Skills />
    </div>
  );
};

export default SkillsPage;
