import { motion, useScroll, useTransform, useMotionValue, useSpring } from 'framer-motion';
import { FaGithub, FaLinkedin, FaEnvelope, FaFileAlt, FaArrowDown, FaCode, FaLaptopCode, FaReact, FaNodeJs, FaPython, FaDatabase } from 'react-icons/fa';
import { SiTailwindcss, SiJavascript, SiTypescript } from 'react-icons/si';
import { Link } from 'react-scroll';
import { useEffect, useState, useRef } from 'react';

// Import custom components and hooks
import AnimatedText from './ui/AnimatedText';
import Card3D from './ui/Card3D';
import { useMagneticEffect } from '../hooks/useAnimations';

const Hero = () => {
  // Typing animation text
  const typingText = "Software Developer | B.Tech CSBS Graduate";

  // Magnetic effect for avatar
  const { ref: avatarRef, handleMouseMove, handleMouseLeave } = useMagneticEffect(0.2);

  // Refs for the hero section and scroll effects
  const sectionRef = useRef(null);
  const containerRef = useRef(null);

  // Mouse position for 3D effects
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Smooth spring physics for mouse movement
  const springConfig = { damping: 25, stiffness: 150 };
  const mouseXSpring = useSpring(mouseX, springConfig);
  const mouseYSpring = useSpring(mouseY, springConfig);

  // Scroll progress for parallax effects
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"]
  });

  // Transform values for scroll-based parallax
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -200]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const y3 = useTransform(scrollYProgress, [0, 1], [0, -300]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  // Matrix rain effect
  const [matrixChars, setMatrixChars] = useState([]);

  // Tech stack icons for floating elements
  const techIcons = [
    <FaReact key="react" size={24} className="text-[#61DAFB]" />,
    <FaNodeJs key="node" size={24} className="text-[#339933]" />,
    <FaPython key="python" size={24} className="text-[#3776AB]" />,
    <SiJavascript key="js" size={24} className="text-[#F7DF1E]" />,
    <SiTypescript key="ts" size={24} className="text-[#3178C6]" />,
    <SiTailwindcss key="tailwind" size={24} className="text-[#06B6D4]" />,
    <FaDatabase key="db" size={24} className="text-[#4479A1]" />
  ];

  // Parallax effect for background elements
  const handleMouseMoveParallax = (e) => {
    if (!sectionRef.current) return;

    const { clientX, clientY } = e;
    const { width, height } = sectionRef.current.getBoundingClientRect();

    // Calculate mouse position relative to the center of the section
    const x = (clientX / width - 0.5) * 2;
    const y = (clientY / height - 0.5) * 2;

    // Update motion values for spring physics
    mouseX.set(x);
    mouseY.set(y);

    // Apply parallax effect to background elements
    const orbs = sectionRef.current.querySelectorAll('.parallax-orb');
    orbs.forEach((orb, index) => {
      const speed = 0.05 * (index + 1);
      orb.style.transform = `translate(${x * 50 * speed}px, ${y * 50 * speed}px)`;
    });

    // Apply 3D rotation to container
    if (containerRef.current) {
      containerRef.current.style.transform = `perspective(1000px) rotateX(${y * 2}deg) rotateY(${-x * 2}deg)`;
    }
  };

  useEffect(() => {
    // Create matrix characters
    const chars = [];
    const charSet = "01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン";

    for (let i = 0; i < 50; i++) {
      chars.push({
        id: i,
        char: charSet[Math.floor(Math.random() * charSet.length)],
        x: Math.random() * 100,
        y: -Math.random() * 100,
        speed: Math.random() * 30 + 10,
        opacity: Math.random() * 0.5 + 0.3,
        size: Math.random() * 1 + 0.7,
      });
    }

    setMatrixChars(chars);
  }, []);

  return (
    <section
      id="hero"
      ref={sectionRef}
      className="min-h-screen flex items-center justify-center relative overflow-hidden perspective-1000"
      onMouseMove={handleMouseMoveParallax}
    >
      {/* Futuristic background with scroll-based parallax */}
      <motion.div className="absolute inset-0 bg-primary" style={{ opacity }}>
        {/* Matrix digital rain effect */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          {matrixChars.map((char) => (
            <motion.div
              key={char.id}
              className="absolute text-secondary font-mono"
              initial={{
                x: `${char.x}vw`,
                y: `${char.y}vh`,
                opacity: char.opacity
              }}
              animate={{
                y: ["0vh", "100vh"]
              }}
              transition={{
                duration: char.speed,
                repeat: Infinity,
                ease: "linear"
              }}
              style={{ fontSize: `${char.size}rem` }}
            >
              {char.char}
            </motion.div>
          ))}
        </div>

        {/* Glowing grid lines */}
        <div className="absolute inset-0 bg-grid-animated opacity-20"></div>

        {/* Animated gradient orbs with parallax effect */}
        <motion.div
          className="parallax-orb absolute top-1/4 left-1/4 w-[30rem] h-[30rem] rounded-full bg-secondary/5 blur-[120px] animate-pulse-slow transition-transform duration-300 ease-out"
          style={{ y: y1 }}
        ></motion.div>
        <motion.div
          className="parallax-orb absolute bottom-1/4 right-1/4 w-[30rem] h-[30rem] rounded-full bg-accent/5 blur-[120px] animate-pulse-slow transition-transform duration-300 ease-out"
          style={{ y: y2, animationDelay: '2s' }}
        ></motion.div>
        <motion.div
          className="parallax-orb absolute top-1/2 right-1/3 w-[20rem] h-[20rem] rounded-full bg-tertiary/5 blur-[100px] animate-pulse-slow transition-transform duration-300 ease-out"
          style={{ y: y3, animationDelay: '4s' }}
        ></motion.div>

        {/* Additional animated elements */}
        <div className="absolute inset-0 bg-gradient-animated opacity-5"></div>

        {/* Animated tech stack icons */}
        <div className="absolute inset-0 overflow-hidden">
          {techIcons.map((icon, i) => (
            <motion.div
              key={i}
              className="absolute opacity-20"
              style={{
                left: `${Math.random() * 90 + 5}%`,
                top: `${Math.random() * 90 + 5}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0.1, 0.3, 0.1],
                scale: [1, 1.2, 1],
                rotate: [0, Math.random() > 0.5 ? 360 : -360, 0]
              }}
              transition={{
                duration: 10 + Math.random() * 20,
                repeat: Infinity,
                delay: i * 0.5,
                ease: "easeInOut"
              }}
            >
              {icon}
            </motion.div>
          ))}
        </div>

        {/* Animated circuit lines */}
        <svg className="absolute inset-0 w-full h-full opacity-10 pointer-events-none" xmlns="http://www.w3.org/2000/svg">
          <motion.path
            d="M0,100 Q150,50 300,100 T600,100 T900,100 T1200,100 T1500,100 T1800,100"
            stroke="rgba(10, 240, 255, 0.5)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 5, ease: "easeInOut" }}
          />
          <motion.path
            d="M0,200 Q150,150 300,200 T600,200 T900,200 T1200,200 T1500,200 T1800,200"
            stroke="rgba(138, 77, 255, 0.5)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 5, delay: 0.5, ease: "easeInOut" }}
          />
          <motion.path
            d="M0,300 Q150,250 300,300 T600,300 T900,300 T1200,300 T1500,300 T1800,300"
            stroke="rgba(255, 94, 98, 0.5)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 5, delay: 1, ease: "easeInOut" }}
          />
        </svg>
      </motion.div>

      {/* Main content with 3D perspective effect */}
      <motion.div
        ref={containerRef}
        className="container mx-auto px-4 z-10 flex flex-col items-center transition-transform duration-300 ease-out"
        style={{
          transformStyle: "preserve-3d",
          transformOrigin: "center center"
        }}
      >
        {/* Animated logo/avatar with 3D effect */}
        <motion.div
          ref={avatarRef}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          initial={{ scale: 0, opacity: 0, rotateY: 180 }}
          animate={{ scale: 1, opacity: 1, rotateY: 0 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.2
          }}
          className="mb-12 relative cursor-pointer"
          style={{ transformStyle: "preserve-3d", perspective: "1000px" }}
        >
          <Card3D
            className="w-40 h-40 rounded-full border-4 border-secondary flex items-center justify-center overflow-hidden relative"
            glowColor="rgba(10, 240, 255, 0.7)"
            glowIntensity={3}
          >
            <div className="text-5xl font-futuristic text-glow-intense transform-gpu" style={{ transform: "translateZ(10px)" }}>KC</div>

            {/* Rotating border effects */}
            <motion.div
              className="absolute inset-0 rounded-full border-4 border-transparent border-t-accent border-r-secondary"
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            ></motion.div>

            <motion.div
              className="absolute inset-[6px] rounded-full border-2 border-transparent border-b-tertiary border-l-secondary"
              animate={{ rotate: -360 }}
              transition={{ duration: 12, repeat: Infinity, ease: "linear" }}
            ></motion.div>

            <motion.div
              className="absolute inset-[12px] rounded-full border border-transparent border-t-secondary/50 border-l-accent/50"
              animate={{ rotate: 720 }}
              transition={{ duration: 16, repeat: Infinity, ease: "linear" }}
            ></motion.div>

            {/* Pulsing circles */}
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute inset-0 rounded-full border border-secondary/30"
                initial={{ scale: 0.8, opacity: 0.8 }}
                animate={{ scale: 1.5 + (i * 0.2), opacity: 0 }}
                transition={{
                  duration: 2 + (i * 0.5),
                  repeat: Infinity,
                  delay: i * 0.6,
                  ease: "easeOut"
                }}
              ></motion.div>
            ))}

            {/* Particle effect */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(10)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 rounded-full bg-secondary/80"
                  initial={{
                    x: "50%",
                    y: "50%",
                    opacity: 0
                  }}
                  animate={{
                    x: `${50 + (Math.random() * 60 - 30)}%`,
                    y: `${50 + (Math.random() * 60 - 30)}%`,
                    opacity: [0, 1, 0]
                  }}
                  transition={{
                    duration: 2 + Math.random() * 2,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>
          </Card3D>

          {/* Enhanced glowing effect */}
          <motion.div
            className="absolute inset-0 rounded-full bg-secondary/20 blur-xl"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.5, 0.8, 0.5]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          ></motion.div>

          {/* Secondary glow */}
          <motion.div
            className="absolute inset-0 rounded-full bg-accent/10 blur-2xl"
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{ duration: 4, repeat: Infinity, delay: 1 }}
          ></motion.div>
        </motion.div>

        {/* Name and title with enhanced animations */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            type: "spring",
            stiffness: 100,
            damping: 15,
            delay: 0.5
          }}
          style={{ transform: "translateZ(20px)" }}
        >
          {/* Name with 3D text effect */}
          <div className="relative mb-6">
            <AnimatedText
              text="Karravula Chandra"
              className="text-6xl md:text-8xl font-futuristic font-bold text-lightest tracking-wider"
              type="glow"
            />

            {/* Text shadow/glow effect */}
            <motion.div
              className="absolute -inset-4 rounded-lg opacity-30 blur-xl bg-gradient-to-r from-secondary/30 via-accent/20 to-tertiary/30"
              animate={{
                opacity: [0.2, 0.4, 0.2],
                scale: [0.95, 1.05, 0.95]
              }}
              transition={{ duration: 4, repeat: Infinity }}
            ></motion.div>
          </div>

          {/* Terminal-style typing effect */}
          <div className="font-mono text-xl text-light/90 flex items-center justify-center bg-dark/30 py-3 px-4 rounded-lg border border-secondary/20 backdrop-blur-sm">
            <motion.span
              className="mr-2 text-accent"
              animate={{ opacity: [1, 0.5, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >&gt;</motion.span>
            <motion.span
              initial={{ width: 0 }}
              animate={{ width: "100%" }}
              transition={{ duration: 2, delay: 1, ease: "easeInOut" }}
              className="overflow-hidden whitespace-nowrap inline-block"
            >
              <AnimatedText
                text={typingText}
                type="typewriter"
                className="text-gradient"
              />
            </motion.span>
            <motion.span
              animate={{ opacity: [0, 1, 0] }}
              transition={{ repeat: Infinity, duration: 1 }}
              className="w-2 h-5 bg-secondary ml-1"
            ></motion.span>
          </div>
        </motion.div>

        {/* Enhanced 3D buttons with advanced hover effects */}
        <motion.div
          className="flex flex-wrap justify-center gap-6 mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            type: "spring",
            stiffness: 100,
            damping: 15,
            delay: 0.8
          }}
          style={{ transform: "translateZ(30px)" }}
        >
          <Link to="projects" smooth={true} duration={500} offset={-70} data-cursor-type="text" data-cursor-text="View">
            <Card3D
              className="overflow-hidden rounded-lg"
              glowColor="rgba(10, 240, 255, 0.7)"
              glowIntensity={2}
              borderGlow={true}
            >
              <motion.button
                className="btn-primary flex items-center relative overflow-hidden group px-8 py-4"
                whileHover={{
                  scale: 1.05,
                  transition: { type: "spring", stiffness: 400, damping: 10 }
                }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="relative z-10 flex items-center">
                  <motion.span
                    className="mr-2 text-glow-intense"
                    animate={{ rotate: [0, 10, 0, -10, 0] }}
                    transition={{ duration: 5, repeat: Infinity }}
                  >&lt;</motion.span>
                  <span className="relative font-bold text-lg">
                    View Projects
                    <motion.span
                      className="absolute bottom-0 left-0 w-full h-[2px] bg-secondary"
                      initial={{ scaleX: 0 }}
                      whileHover={{ scaleX: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  </span>
                  <motion.span
                    className="ml-2 text-glow-intense"
                    animate={{ rotate: [0, -10, 0, 10, 0] }}
                    transition={{ duration: 5, repeat: Infinity, delay: 0.5 }}
                  >/&gt;</motion.span>
                </span>

                {/* Animated particles inside button */}
                <div className="absolute inset-0 pointer-events-none overflow-hidden">
                  {[...Array(5)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 rounded-full bg-secondary/80"
                      initial={{
                        x: "50%",
                        y: "50%",
                        opacity: 0
                      }}
                      animate={{
                        x: `${Math.random() * 100}%`,
                        y: `${Math.random() * 100}%`,
                        opacity: [0, 0.8, 0]
                      }}
                      transition={{
                        duration: 2 + Math.random() * 2,
                        repeat: Infinity,
                        delay: i * 0.3,
                        ease: "easeInOut"
                      }}
                    />
                  ))}
                </div>
              </motion.button>
            </Card3D>
          </Link>

          <Link to="contact" smooth={true} duration={500} offset={-70} data-cursor-type="text" data-cursor-text="Contact">
            <Card3D
              className="overflow-hidden rounded-lg"
              glowColor="rgba(138, 77, 255, 0.7)"
              glowIntensity={2}
              borderGlow={true}
            >
              <motion.button
                className="btn-secondary flex items-center relative overflow-hidden group px-8 py-4"
                whileHover={{
                  scale: 1.05,
                  transition: { type: "spring", stiffness: 400, damping: 10 }
                }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="relative z-10 flex items-center">
                  <motion.span
                    className="mr-2 text-accent"
                    animate={{ rotate: [0, 10, 0, -10, 0] }}
                    transition={{ duration: 5, repeat: Infinity, delay: 0.2 }}
                  >&lt;</motion.span>
                  <span className="relative font-bold text-lg">
                    Contact Me
                    <motion.span
                      className="absolute bottom-0 left-0 w-full h-[2px] bg-accent"
                      initial={{ scaleX: 0 }}
                      whileHover={{ scaleX: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  </span>
                  <motion.span
                    className="ml-2 text-accent"
                    animate={{ rotate: [0, -10, 0, 10, 0] }}
                    transition={{ duration: 5, repeat: Infinity, delay: 0.7 }}
                  >/&gt;</motion.span>
                </span>

                {/* Animated gradient background */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-accent/10 via-tertiary/10 to-accent/10 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  animate={{ backgroundPosition: ['0% center', '100% center'] }}
                  transition={{ duration: 3, repeat: Infinity, repeatType: "mirror" }}
                  style={{ backgroundSize: '200% 100%' }}
                />
              </motion.button>
            </Card3D>
          </Link>

          <a href="/resume.pdf" target="_blank" rel="noopener noreferrer" data-cursor-type="text" data-cursor-text="Download">
            <Card3D
              className="overflow-hidden rounded-lg"
              glowColor="rgba(255, 94, 98, 0.5)"
              glowIntensity={2}
              borderGlow={true}
            >
              <motion.button
                className="btn-outline flex items-center group px-8 py-4"
                whileHover={{
                  scale: 1.05,
                  transition: { type: "spring", stiffness: 400, damping: 10 }
                }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 5, repeat: Infinity, ease: "linear" }}
                  className="mr-3 text-tertiary"
                >
                  <FaFileAlt size={18} />
                </motion.div>
                <span className="relative font-bold text-lg">
                  Resume
                  <motion.span
                    className="absolute bottom-0 left-0 w-full h-[1px] bg-tertiary"
                    initial={{ scaleX: 0 }}
                    whileHover={{ scaleX: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                </span>

                {/* Download icon that appears on hover */}
                <motion.div
                  className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={{ x: -10 }}
                  whileHover={{ x: 0 }}
                  animate={{ y: [0, -3, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <FaArrowDown className="text-tertiary" />
                </motion.div>
              </motion.button>
            </Card3D>
          </a>
        </motion.div>

        {/* Enhanced social links with interactive effects */}
        <motion.div
          className="flex space-x-8 mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            type: "spring",
            stiffness: 100,
            damping: 15,
            delay: 1
          }}
          style={{ transform: "translateZ(40px)" }}
        >
          {/* GitHub */}
          <Card3D
            className="rounded-full overflow-hidden"
            glowColor="rgba(10, 240, 255, 0.7)"
            intensity={12}
            borderGlow={true}
          >
            <motion.a
              href="https://github.com/karrachandrasekhar"
              target="_blank"
              rel="noopener noreferrer"
              className="text-light hover:text-secondary transition-all duration-300 p-4 border border-light/20 hover:border-secondary rounded-full hover:shadow-neon block bg-dark/50 backdrop-blur-sm"
              aria-label="GitHub Profile"
              data-cursor-type="text"
              data-cursor-text="GitHub"
              data-cursor-color="secondary"
              whileHover={{
                scale: 1.1,
                rotate: 10,
                transition: { type: "spring", stiffness: 400, damping: 10 }
              }}
              whileTap={{ scale: 0.9 }}
            >
              <motion.div
                animate={{
                  rotate: [0, 10, 0, -10, 0],
                  scale: [1, 1.1, 1, 1.1, 1]
                }}
                transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
              >
                <FaGithub size={28} className="text-glow-intense" />
              </motion.div>
            </motion.a>
          </Card3D>

          {/* LinkedIn */}
          <Card3D
            className="rounded-full overflow-hidden"
            glowColor="rgba(138, 77, 255, 0.7)"
            intensity={12}
            borderGlow={true}
          >
            <motion.a
              href="https://linkedin.com/in/karra-chandra-sekhar"
              target="_blank"
              rel="noopener noreferrer"
              className="text-light hover:text-accent transition-all duration-300 p-4 border border-light/20 hover:border-accent rounded-full hover:shadow-neon block bg-dark/50 backdrop-blur-sm"
              aria-label="LinkedIn Profile"
              data-cursor-type="text"
              data-cursor-text="LinkedIn"
              data-cursor-color="accent"
              whileHover={{
                scale: 1.1,
                rotate: -10,
                transition: { type: "spring", stiffness: 400, damping: 10 }
              }}
              whileTap={{ scale: 0.9 }}
            >
              <motion.div
                animate={{
                  rotate: [0, -10, 0, 10, 0],
                  scale: [1, 1.1, 1, 1.1, 1]
                }}
                transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
              >
                <FaLinkedin size={28} className="text-accent" />
              </motion.div>
            </motion.a>
          </Card3D>

          {/* Email */}
          <Card3D
            className="rounded-full overflow-hidden"
            glowColor="rgba(255, 94, 98, 0.7)"
            intensity={12}
            borderGlow={true}
          >
            <motion.a
              href="mailto:<EMAIL>"
              className="text-light hover:text-tertiary transition-all duration-300 p-4 border border-light/20 hover:border-tertiary rounded-full hover:shadow-neon block bg-dark/50 backdrop-blur-sm"
              aria-label="Email Me"
              data-cursor-type="text"
              data-cursor-text="Email"
              data-cursor-color="tertiary"
              whileHover={{
                scale: 1.1,
                rotate: 10,
                transition: { type: "spring", stiffness: 400, damping: 10 }
              }}
              whileTap={{ scale: 0.9 }}
            >
              <motion.div
                animate={{
                  rotate: [0, 10, 0, -10, 0],
                  scale: [1, 1.1, 1, 1.1, 1]
                }}
                transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
              >
                <FaEnvelope size={28} className="text-tertiary" />
              </motion.div>
            </motion.a>
          </Card3D>
        </motion.div>

        {/* Floating tech elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(15)].map((_, i) => {
            const icons = [
              <FaCode key="code" size={20} />,
              <FaLaptopCode key="laptop" size={20} />,
              <span key="braces" className="font-mono">{"{ }"}</span>,
              <span key="tags" className="font-mono">{"</>"}</span>,
              <span key="binary" className="font-mono">{"01"}</span>,
              <span key="function" className="font-mono">{"() => {}"}</span>,
              <span key="class" className="font-mono">{"class {}"}</span>
            ];

            // Create different animation paths for each element
            const randomPath = () => {
              const pathType = Math.floor(Math.random() * 4);

              switch(pathType) {
                case 0: // Circular
                  return {
                    x: [0, 50, 0, -50, 0],
                    y: [0, 50, 100, 50, 0],
                    rotate: [0, 180, 360, 540, 720]
                  };
                case 1: // Zigzag
                  return {
                    x: [0, 50, -50, 50, 0],
                    y: [0, 25, 50, 75, 100],
                    rotate: [0, 90, 180, 270, 360]
                  };
                case 2: // Spiral
                  return {
                    x: [0, 20, 0, -20, 0],
                    y: [0, 20, 40, 20, 0],
                    scale: [1, 1.2, 1.5, 1.2, 1],
                    rotate: [0, 90, 180, 270, 360]
                  };
                default: // Random
                  return {
                    x: [0, Math.random() * 100 - 50, Math.random() * 100 - 50, Math.random() * 100 - 50, 0],
                    y: [0, Math.random() * 100, Math.random() * 100, Math.random() * 100, 0],
                    rotate: [0, Math.random() * 360, Math.random() * 360, Math.random() * 360, 0]
                  };
              }
            };

            const path = randomPath();
            const duration = Math.random() * 30 + 20;
            const delay = Math.random() * 5;

            return (
              <motion.div
                key={i}
                className="absolute text-secondary/30 backdrop-blur-sm p-2 rounded-md"
                style={{
                  left: `${Math.random() * 90}%`,
                  top: `${Math.random() * 90}%`,
                }}
                animate={path}
                transition={{
                  duration,
                  delay,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              >
                {icons[i % icons.length]}
              </motion.div>
            );
          })}
        </div>

        {/* Enhanced scroll indicator with 3D effects */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            type: "spring",
            stiffness: 100,
            damping: 15,
            delay: 1.5
          }}
          className="absolute bottom-10"
          style={{ transform: "translateZ(50px)" }}
        >
          <Link to="about" smooth={true} duration={800} className="cursor-pointer" data-cursor-type="text" data-cursor-text="Scroll">
            <Card3D
              className="rounded-xl overflow-hidden"
              glowColor="rgba(10, 240, 255, 0.7)"
              intensity={10}
              borderGlow={true}
            >
              <motion.div
                className="flex flex-col items-center text-secondary px-6 py-3 bg-dark/50 backdrop-blur-sm border border-secondary/30"
                whileHover={{
                  scale: 1.05,
                  transition: { type: "spring", stiffness: 400, damping: 10 }
                }}
              >
                <motion.span
                  className="font-mono text-sm mb-2 text-glow-intense font-bold"
                  animate={{
                    textShadow: [
                      '0 0 5px rgba(10, 240, 255, 0.5), 0 0 10px rgba(10, 240, 255, 0.3)',
                      '0 0 15px rgba(10, 240, 255, 0.8), 0 0 30px rgba(10, 240, 255, 0.5)',
                      '0 0 5px rgba(10, 240, 255, 0.5), 0 0 10px rgba(10, 240, 255, 0.3)'
                    ]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  Explore
                </motion.span>

                {/* Animated arrow with trail effect */}
                <div className="relative h-8 w-8 flex items-center justify-center">
                  {/* Arrow trails */}
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute"
                      initial={{ y: -10, opacity: 0 }}
                      animate={{
                        y: 10,
                        opacity: [0, 0.5, 0]
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        delay: i * 0.2,
                        ease: "easeOut"
                      }}
                    >
                      <FaArrowDown className="text-secondary/50" size={14} />
                    </motion.div>
                  ))}

                  {/* Main arrow */}
                  <motion.div
                    animate={{
                      y: [0, 10, 0],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      repeatType: "mirror",
                      ease: "easeInOut"
                    }}
                  >
                    <FaArrowDown className="text-secondary" size={16} />
                  </motion.div>
                </div>

                {/* Animated circle */}
                <motion.div
                  className="absolute inset-0 rounded-full border-2 border-secondary/30"
                  animate={{
                    scale: [0.8, 1.2, 0.8],
                    opacity: [0.3, 0.1, 0.3]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            </Card3D>
          </Link>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;
