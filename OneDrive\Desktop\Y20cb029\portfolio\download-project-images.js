const fs = require('fs');
const path = require('path');
const https = require('https');

// Create the projects directory if it doesn't exist
const projectsDir = path.join(__dirname, 'public', 'images', 'projects');
if (!fs.existsSync(projectsDir)) {
  fs.mkdirSync(projectsDir, { recursive: true });
  console.log(`Created directory: ${projectsDir}`);
}

// Project images to download
const projectImages = [
  {
    name: 'sports-portfolio.jpg',
    url: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Sports Portfolio - A responsive sports portfolio website'
  },
  {
    name: 'portfolio-showcase.jpg',
    url: 'https://images.unsplash.com/photo-1507238691740-187a5b1d37b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Portfolio Showcase - A professional portfolio website template'
  },
  {
    name: 'chat-checkpoints.jpg',
    url: 'https://images.unsplash.com/photo-1611606063065-ee7946f0787a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Chat Checkpoints - A real-time chat application'
  },
  {
    name: 'indapp.jpg',
    url: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'IndApp - A mobile application for service providers'
  },
  {
    name: 'gcp-project.jpg',
    url: 'https://images.unsplash.com/photo-**********-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'GCP Project - A cloud-based application'
  },
  {
    name: 'web-development.jpg',
    url: 'https://images.unsplash.com/photo-**********-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Web Development - Collection of web development projects'
  },
  {
    name: 'vcare.jpg',
    url: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'VCare - A healthcare management system'
  },
  {
    name: 'village-agency.jpg',
    url: 'https://images.unsplash.com/photo-1516937941344-00b4e0337589?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Village Agency - A platform for rural communities'
  },
  {
    name: 'java-project.jpg',
    url: 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Java Project - A comprehensive Java application'
  },
  {
    name: 'mlops-fundamentals.jpg',
    url: 'https://images.unsplash.com/photo-**********-ff9fe0c870eb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'MLOps Fundamentals - A project exploring MLOps practices'
  },
  {
    name: 'java-lab-exercise.jpg',
    url: 'https://images.unsplash.com/photo-1515879218367-8466d910aaa4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Java Lab Exercise - A collection of Java programming exercises'
  },
  {
    name: 'malware-detection.jpg',
    url: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    description: 'Malware Detection with ML - An advanced malware detection system'
  }
];

// Function to download an image
function downloadImage(imageUrl, imagePath, description) {
  return new Promise((resolve, reject) => {
    https.get(imageUrl, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${description}: Status code ${response.statusCode}`));
        return;
      }

      const fileStream = fs.createWriteStream(imagePath);
      response.pipe(fileStream);

      fileStream.on('finish', () => {
        fileStream.close();
        resolve();
      });

      fileStream.on('error', (err) => {
        fs.unlink(imagePath, () => {}); // Delete the file if there was an error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Download all images
async function downloadAllImages() {
  console.log('Starting to download project images...');
  
  for (const image of projectImages) {
    const imagePath = path.join(projectsDir, image.name);
    
    try {
      // Check if the file already exists
      if (fs.existsSync(imagePath)) {
        console.log(`${image.name} already exists, skipping...`);
        continue;
      }
      
      console.log(`Downloading ${image.description}...`);
      await downloadImage(image.url, imagePath, image.description);
      console.log(`Successfully downloaded ${image.name}`);
    } catch (error) {
      console.error(`Error downloading ${image.name}:`, error.message);
    }
  }
  
  console.log('All downloads completed!');
}

// Run the download function
downloadAllImages();
