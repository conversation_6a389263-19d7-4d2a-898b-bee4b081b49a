const Education = () => {
  // Comprehensive education data
  const educationData = [
    {
      degree: "Bachelor of Technology (B.Tech)",
      field: "Computer Science and Business Systems (CSBS), Minor in Cloud Computing",
      institution: "RVR&JC College of Engineering",
      location: "Guntur, Andhra Pradesh",
      period: "2020 - 2024",
      gpa: "8.63/10",
      description: "Completed a comprehensive program covering computer science fundamentals, software engineering, database systems, and business applications of technology. Specialized in cloud computing as a minor. Participated in various technical competitions, hackathons, and research projects. Expected graduation in May 2024.",
      achievements: [
        "Developed an AI-powered healthcare diagnostic system as final year project",
        "Won first place in the university-level hackathon for innovative solutions",
        "Published a research paper on machine learning applications in healthcare",
        "Served as Student Representative for the Computer Science Department",
        "Organized technical workshops and coding competitions"
      ],
      courses: [
        "Data Structures and Algorithms",
        "Object-Oriented Programming",
        "Database Management Systems",
        "Web Development",
        "Software Engineering",
        "Business Intelligence",
        "Cloud Computing",
        "Machine Learning",
        "Deep Learning",
        "Natural Language Processing",
        "Computer Vision",
        "Artificial Intelligence",
        "Computer Networks",
        "Operating Systems",
        "Cybersecurity",
        "Big Data Analytics",
        "Distributed Systems",
        "Mobile Application Development",
        "Software Project Management",
        "Business Systems Analysis"
      ]
    },
    {
      degree: "Machine Learning Specialization",
      field: "Artificial Intelligence",
      institution: "Stanford University (Online)",
      location: "Coursera",
      period: "2022 - 2023",
      gpa: "98%",
      description: "Completed an intensive specialization in machine learning taught by Andrew Ng, covering supervised learning, unsupervised learning, and reinforcement learning. Implemented various machine learning algorithms from scratch and applied them to real-world problems.",
      achievements: [
        "Achieved perfect scores on all programming assignments",
        "Developed a recommendation system as capstone project",
        "Implemented neural networks from scratch",
        "Created a portfolio of machine learning projects"
      ],
      courses: [
        "Supervised Machine Learning: Regression and Classification",
        "Advanced Learning Algorithms",
        "Unsupervised Learning, Recommenders, Reinforcement Learning"
      ]
    },
    {
      degree: "Deep Learning Specialization",
      field: "Artificial Intelligence",
      institution: "DeepLearning.AI (Online)",
      location: "Coursera",
      period: "2022",
      gpa: "95%",
      description: "Completed a comprehensive specialization in deep learning, covering neural networks, convolutional neural networks, sequence models, and more. Implemented various deep learning architectures and applied them to computer vision and natural language processing tasks.",
      achievements: [
        "Built and trained neural networks for image recognition",
        "Implemented sequence models for natural language processing",
        "Developed a neural style transfer application",
        "Created a speech recognition system"
      ],
      courses: [
        "Neural Networks and Deep Learning",
        "Improving Deep Neural Networks",
        "Structuring Machine Learning Projects",
        "Convolutional Neural Networks",
        "Sequence Models"
      ]
    },
    {
      degree: "Higher Secondary Education",
      field: "Science (Mathematics, Physics, Chemistry)",
      institution: "Sri Chaitanya Junior College",
      location: "Hyderabad, India",
      period: "2017 - 2019",
      gpa: "95%",
      description: "Completed higher secondary education with a focus on science and mathematics, building a strong foundation for engineering studies. Ranked among the top students in the institution and received merit scholarship for academic excellence.",
      achievements: [
        "Ranked in the top 5% of students in state-level examinations",
        "Won first prize in inter-college mathematics competition",
        "Participated in National Science Olympiad",
        "Selected for special mentorship program for gifted students",
        "Completed advanced courses in mathematics and computer science"
      ],
      courses: [
        "Mathematics",
        "Physics",
        "Chemistry",
        "Computer Science",
        "English",
        "Advanced Calculus",
        "Statistics and Probability"
      ]
    },
    {
      degree: "Secondary Education",
      field: "General Education",
      institution: "Narayana High School",
      location: "Hyderabad, India",
      period: "2015 - 2017",
      gpa: "92%",
      description: "Completed secondary education with distinction, developing strong analytical and problem-solving skills. Participated in various extracurricular activities and competitions.",
      achievements: [
        "Received academic excellence award for consistent performance",
        "Represented school in district-level science exhibition",
        "Active member of the school's computer club",
        "Participated in mathematics and science competitions",
        "Developed interest in programming and technology"
      ],
      courses: [
        "Mathematics",
        "Science",
        "Social Studies",
        "English",
        "Computer Applications",
        "Environmental Science"
      ]
    }
  ];

  return (
    <div className="py-10">
      <h1 className="text-4xl font-bold mb-8 text-center">
        <span className="text-blue-400">My</span> Education
      </h1>

      <p className="text-gray-300 text-center max-w-3xl mx-auto mb-12 leading-relaxed">
        My educational background has provided me with a strong foundation in computer science,
        programming, and problem-solving skills.
      </p>

      <div className="space-y-12">
        {educationData.map((edu, index) => (
          <EducationCard key={index} education={edu} />
        ))}
      </div>

      <div className="mt-16 bg-blue-900 bg-opacity-40 p-6 rounded-lg shadow-lg">
        <h2 className="text-2xl font-semibold mb-6 text-blue-300">Additional Certifications</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <CertificationItem
            title="Full Stack Web Development"
            issuer="Udemy"
            date="2022"
            description="Comprehensive course covering modern web development technologies including React, Node.js, and MongoDB."
          />
          <CertificationItem
            title="Python for Data Science"
            issuer="Coursera"
            date="2021"
            description="Learned Python programming for data analysis, visualization, and basic machine learning applications."
          />
          <CertificationItem
            title="AWS Cloud Practitioner"
            issuer="Amazon Web Services"
            date="2022"
            description="Fundamental understanding of AWS cloud services, security, architecture, and pricing models."
          />
          <CertificationItem
            title="UI/UX Design Fundamentals"
            issuer="Google"
            date="2021"
            description="Principles of user interface design, user experience, and creating effective digital products."
          />
        </div>
      </div>
    </div>
  );
};

const EducationCard = ({ education }) => {
  return (
    <div className="glass-card-dark p-6 rounded-lg shadow-lg hover-lift mb-8">
      <div className="flex flex-col md:flex-row justify-between mb-4">
        <div>
          <h2 className="text-2xl font-bold text-gradient-alt animate-neon">{education.degree}</h2>
          <h3 className="text-xl text-blue-300">{education.field}</h3>
        </div>
        <div className="text-right mt-2 md:mt-0">
          <div className="text-gray-300">{education.period}</div>
          <div className="text-blue-400 font-medium">GPA: {education.gpa}</div>
        </div>
      </div>

      <div className="mb-4">
        <div className="text-lg font-medium text-white">{education.institution}</div>
        <div className="text-gray-400">{education.location}</div>
      </div>

      <p className="text-gray-300 mb-6 leading-relaxed">
        {education.description}
      </p>

      {education.achievements && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-gradient mb-3">Key Achievements</h4>
          <ul className="space-y-2">
            {education.achievements.map((achievement, index) => (
              <li
                key={index}
                className="flex items-start"
              >
                <span className="text-blue-400 mr-2">•</span>
                <span className="text-gray-300">{achievement}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      <div>
        <h4 className="text-lg font-semibold text-gradient mb-3">Key Courses</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          {education.courses.map((course, index) => (
            <div
              key={index}
              className="bg-blue-800/30 backdrop-blur-sm px-3 py-2 rounded-lg text-gray-200 text-sm border border-blue-700/30 hover:border-blue-500/50 transition-all duration-300"
            >
              {course}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const CertificationItem = ({ title, issuer, date, description }) => {
  return (
    <div className="glass-card-dark p-4 rounded-lg hover-lift border border-blue-700/30 hover:border-blue-500/50 transition-all duration-300">
      <h3 className="text-lg font-semibold text-gradient mb-1">{title}</h3>
      <div className="flex justify-between text-sm mb-2">
        <span className="text-blue-300">{issuer}</span>
        <span className="text-gray-400">{date}</span>
      </div>
      <p className="text-gray-300 text-sm">{description}</p>
    </div>
  );
};

export default Education;
