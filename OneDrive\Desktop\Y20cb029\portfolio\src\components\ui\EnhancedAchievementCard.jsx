import { motion } from 'framer-motion';
import { FiExternalLink, FiCalendar, FiMapPin, FiAward, FiUser } from 'react-icons/fi';

const EnhancedAchievementCard = ({ item, index, type, onClick }) => {
  const getTypeIcon = () => {
    switch (type) {
      case 'certifications':
        return FiAward;
      case 'internships':
        return FiUser;
      case 'achievements':
        return FiAward;
      default:
        return FiAward;
    }
  };

  const getTypeColor = () => {
    switch (type) {
      case 'certifications':
        return 'blue';
      case 'internships':
        return 'green';
      case 'achievements':
        return 'purple';
      default:
        return 'blue';
    }
  };

  const TypeIcon = getTypeIcon();
  const color = getTypeColor();

  const handleClick = () => {
    if (onClick) {
      onClick(item);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -8, scale: 1.02 }}
      className="group cursor-pointer"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${item.title}`}
    >
      <div className="bg-white dark:bg-gray-800 rounded-2xl overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-500 hover:shadow-2xl h-full">
        {/* Image Container */}
        <div className="relative h-48 overflow-hidden">
          <img
            src={item.image}
            alt={item.title}
            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `/images/placeholder-${type}.jpg`;
            }}
          />
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
          
          {/* Type Icon */}
          <div className={`absolute top-4 left-4 w-10 h-10 bg-${color}-500/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-${color}-500/30`}>
            <TypeIcon className={`w-5 h-5 text-${color}-400`} />
          </div>
          
          {/* Year Badge */}
          <div className={`absolute top-4 right-4 bg-${color}-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg`}>
            {item.year}
          </div>

          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-6">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileHover={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
              className={`flex items-center gap-2 text-${color}-400 font-medium`}
            >
              <FiExternalLink className="w-4 h-4" />
              <span className="text-sm">View Details</span>
            </motion.div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Title */}
          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 line-clamp-2">
            {item.title}
          </h3>
          
          {/* Subtitle */}
          <div className="flex items-center gap-2 mb-3">
            <FiCalendar className="w-4 h-4 text-gray-400" />
            <p className={`text-${color}-600 dark:text-${color}-400 font-medium text-sm`}>
              {type === 'certifications' && item.issuer}
              {type === 'internships' && `${item.company} • ${item.duration}`}
              {type === 'achievements' && item.event}
            </p>
          </div>
          
          {/* Description */}
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
            {item.description}
          </p>

          {/* Skills */}
          {item.skills && item.skills.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {item.skills.slice(0, 3).map((skill, skillIndex) => (
                <motion.span
                  key={skillIndex}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: skillIndex * 0.1 }}
                  className={`px-3 py-1 bg-${color}-50 dark:bg-${color}-900/20 text-${color}-700 dark:text-${color}-300 text-xs rounded-full font-medium border border-${color}-200 dark:border-${color}-800`}
                >
                  {skill}
                </span>
              ))}
              {item.skills.length > 3 && (
                <span className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full font-medium">
                  +{item.skills.length - 3}
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedAchievementCard;
