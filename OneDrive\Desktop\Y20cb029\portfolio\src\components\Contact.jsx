import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaEnvelope, FaLinkedin, FaGithub, FaTwitter, FaPhoneAlt, FaMapMarkerAlt, FaPaperPlane, FaCode } from 'react-icons/fa';
import { useState, useRef, useEffect } from 'react';
import { motion, useInView, useMotionValue, useSpring, AnimatePresence } from 'framer-motion';
import { 
  FaEnvelope, FaLinkedin, FaGithub, FaPhoneAlt, FaPaperPlane, 
  FaCode, FaCheckCircle, FaExclamationTriangle, FaSpinner,
  FaMapMarkerAlt, FaClock, FaGlobe, FaHeart
} from 'react-icons/fa';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);
  const [errors, setErrors] = useState({});
  const [focusedField, setFocusedField] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const formRef = useRef(null);
  const containerRef = useRef(null);
  const isInView = useInView(formRef, { once: false, margin: "-100px" });

  // Advanced mouse tracking for interactive effects
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMousePosition({
          x: ((e.clientX - rect.left) / rect.width) * 100,
          y: ((e.clientY - rect.top) / rect.height) * 100
        });
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      return () => container.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  // Advanced form validation
  const validateField = (name, value) => {
    const newErrors = { ...errors };
    
    switch (name) {
      case 'name':
        if (!value.trim()) {
          newErrors.name = 'Name is required';
        } else if (value.trim().length < 2) {
          newErrors.name = 'Name must be at least 2 characters';
        } else {
          delete newErrors.name;
        }
        break;
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!value.trim()) {
          newErrors.email = 'Email is required';
        } else if (!emailRegex.test(value)) {
          newErrors.email = 'Please enter a valid email address';
        } else {
          delete newErrors.email;
        }
        break;
      case 'subject':
        if (!value.trim()) {
          newErrors.subject = 'Subject is required';
        } else if (value.trim().length < 5) {
          newErrors.subject = 'Subject must be at least 5 characters';
        } else {
          delete newErrors.subject;
        }
        break;
      case 'message':
        if (!value.trim()) {
          newErrors.message = 'Message is required';
        } else if (value.trim().length < 10) {
          newErrors.message = 'Message must be at least 10 characters';
        } else {
          delete newErrors.message;
        }
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Real-time validation
    if (value.trim()) {
      validateField(name, value);
    }
  };

  const handleSubmit = (e) => {
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate all fields
    const isValid = Object.keys(formData).every(key => 
      validateField(key, formData[key])
    );
    
    if (!isValid) {
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus(null);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
    // Simulate API call with realistic timing
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });

      // Reset status after 5 seconds
      setTimeout(() => {
        setSubmitStatus(null);
      }, 5000);
    }, 1500);
      setErrors({});
      
      // Auto-hide success message
      setTimeout(() => setSubmitStatus(null), 6000);
    } catch (error) {
      setSubmitStatus('error');
      setTimeout(() => setSubmitStatus(null), 5000);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  // Professional animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
        staggerChildren: 0.08,
        delayChildren: 0.2,
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
    hidden: { 
      y: 30, 
      opacity: 0,
      scale: 0.95
    },
    visible: { 
      y: 0, 
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [-2, 2, -2],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <section id="contact" className="py-20 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 cyber-grid opacity-10"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 rounded-full bg-secondary/5 blur-[100px] animate-pulse-slow"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 rounded-full bg-accent/5 blur-[100px] animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
    <section id="contact" className="py-32 relative overflow-hidden min-h-screen" ref={containerRef}>
      {/* Advanced background with parallax effect */}
      <div className="absolute inset-0">
        {/* Animated gradient background */}
        <motion.div 
          className="absolute inset-0 opacity-30"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(10, 240, 255, 0.1) 0%, rgba(138, 77, 255, 0.05) 50%, transparent 100%)`
          }}
        />
        
        {/* Dynamic grid */}
        <div className="absolute inset-0 cyber-grid opacity-[0.03]" />
        
        {/* Floating geometric shapes */}
        <motion.div
          variants={floatingVariants}
          animate="animate"
          className="absolute top-20 left-[10%] w-32 h-32 rounded-full bg-gradient-to-r from-secondary/10 to-accent/10 blur-xl"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: '2s' }}
          className="absolute bottom-32 right-[15%] w-48 h-48 rounded-full bg-gradient-to-r from-accent/10 to-tertiary/10 blur-xl"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: '4s' }}
          className="absolute top-1/2 left-[5%] w-24 h-24 rounded-full bg-gradient-to-r from-highlight/10 to-secondary/10 blur-xl"
        />
      </div>

      {/* Decorative code elements */}
      <div className="absolute top-20 left-10 text-secondary/10 text-6xl hidden lg:block">
      {/* Decorative code elements with advanced positioning */}
      <motion.div 
        className="absolute top-24 left-12 text-secondary/8 text-8xl hidden xl:block"
        animate={{ 
          rotate: [0, 5, 0, -5, 0],
          scale: [1, 1.1, 1, 1.05, 1]
        }}
        transition={{ duration: 8, repeat: Infinity }}
      >
        <FaCode />
      </div>
      <div className="absolute bottom-20 right-10 text-accent/10 text-6xl hidden lg:block">
        &lt;/&gt;
      </div>
      </motion.div>
      <motion.div 
        className="absolute bottom-24 right-12 text-accent/8 text-8xl hidden xl:block font-mono"
        animate={{ 
          rotate: [0, -5, 0, 5, 0],
          scale: [1, 1.05, 1, 1.1, 1]
        }}
        transition={{ duration: 10, repeat: Infinity, delay: 2 }}
      >
        {'</>'} 
      </motion.div>

      <div className="section-container relative z-10">
        {/* Enhanced header section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
          transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
          className="text-center mb-20"
        >
          <h2 className="section-title">
            <span className="text-secondary font-mono">07.</span> Contact
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-block mb-6"
          >
            <span className="text-secondary font-mono text-lg font-medium tracking-wider">
              {'<section id="contact">'}
            </span>
          </motion.div>
          
          <h2 className="section-title text-6xl md:text-7xl lg:text-8xl font-futuristic mb-8">
            <motion.span
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-secondary font-mono text-2xl md:text-3xl block mb-4"
            >
              07.
            </motion.span>
            <motion.span
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-gradient-to-r from-lightest via-secondary to-accent bg-clip-text text-transparent"
            >
              Let's Connect
            </motion.span>
          </h2>
          <p className="text-light/70 max-w-2xl mx-auto mt-4">
            Have a question or want to work together? Feel free to reach out using the form below or through any of my social channels.
          </p>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-light/80 max-w-3xl mx-auto text-lg md:text-xl leading-relaxed"
          >
            Ready to bring your next project to life? I'm always excited to discuss new opportunities, 
            collaborate on innovative solutions, or simply connect with fellow developers and creators.
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-8"
          >
            <span className="text-secondary font-mono text-lg">
              {'</section>'}
            </span>
          </motion.div>
        </motion.div>

        <div className="grid lg:grid-cols-5 gap-10">
          {/* Contact Form - 3 columns */}
        <div className="grid lg:grid-cols-12 gap-12 lg:gap-16">
          {/* Enhanced Contact Form - 7 columns */}
          <motion.div
            ref={formRef}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
            className="lg:col-span-3"
            className="lg:col-span-7"
          >
            <motion.div
              variants={itemVariants}
              className="glass-card backdrop-blur-md"
              className="relative group"
            >
              <h3 className="text-2xl font-futuristic text-lightest mb-6 flex items-center">
                <FaPaperPlane className="text-secondary mr-3" />
                Send Me a Message
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <motion.div variants={itemVariants}>
                    <label htmlFor="name" className="block text-light mb-2 font-medium">Your Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-dark/70 border border-secondary/20 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent text-light transition-all duration-300"
                      placeholder="John Doe"
                    />
              {/* Advanced glass card with better effects */}
              <div className="absolute inset-0 bg-gradient-to-br from-secondary/5 via-transparent to-accent/5 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
              <div className="relative glass-card backdrop-blur-xl border border-secondary/20 group-hover:border-secondary/40 transition-all duration-500 rounded-2xl overflow-hidden">
                {/* Card header with icon */}
                <div className="relative p-8 pb-6 border-b border-secondary/10">
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                    className="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-secondary/20 to-accent/20 rounded-full flex items-center justify-center backdrop-blur-sm"
                  >
                    <FaPaperPlane className="text-2xl text-secondary" />
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <label htmlFor="email" className="block text-light mb-2 font-medium">Your Email</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-dark/70 border border-secondary/20 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent text-light transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </motion.div>
                  
                  <h3 className="text-3xl font-futuristic text-lightest mb-3 flex items-center">
                    <motion.div
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                      className="mr-4 p-3 bg-secondary/10 rounded-lg"
                    >
                      <FaEnvelope className="text-secondary text-xl" />
                    </motion.div>
                    Send Message
                  </h3>
                  <p className="text-light/70 text-lg">
                    Fill out the form below and I'll get back to you within 24 hours.
                  </p>
                </div>

                <motion.div variants={itemVariants}>
                  <label htmlFor="subject" className="block text-light mb-2 font-medium">Subject</label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-dark/70 border border-secondary/20 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent text-light transition-all duration-300"
                    placeholder="Project Inquiry / Job Opportunity"
                  />
                </motion.div>
                {/* Enhanced form */}
                <form onSubmit={handleSubmit} className="p-8 space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Name field */}
                    <motion.div variants={itemVariants} className="relative group">
                      <label 
                        htmlFor="name" 
                        className="block text-light mb-3 font-medium text-lg transition-colors duration-300 group-focus-within:text-secondary"
                      >
                        Your Name *
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          onFocus={() => setFocusedField('name')}
                          onBlur={() => setFocusedField(null)}
                          required
                          className={`w-full px-6 py-4 bg-dark/70 border-2 rounded-xl focus:outline-none text-light transition-all duration-300 text-lg
                            ${errors.name 
                              ? 'border-red-500 focus:border-red-400 focus:ring-2 focus:ring-red-400/20' 
                              : focusedField === 'name' 
                                ? 'border-secondary focus:border-secondary focus:ring-2 focus:ring-secondary/20' 
                                : 'border-secondary/20 hover:border-secondary/40'
                            }`}
                          placeholder="John Doe"
                        />
                        <AnimatePresence>
                          {focusedField === 'name' && (
                            <motion.div
                              initial={{ scale: 0, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              exit={{ scale: 0, opacity: 0 }}
                              className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full"
                            />
                          )}
                        </AnimatePresence>
                      </div>
                      <AnimatePresence>
                        {errors.name && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="mt-2 text-red-400 text-sm flex items-center"
                          >
                            <FaExclamationTriangle className="mr-2" />
                            {errors.name}
                          </motion.p>
                        )}
                      </AnimatePresence>
                    </motion.div>

                <motion.div variants={itemVariants}>
                  <label htmlFor="message" className="block text-light mb-2 font-medium">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows="6"
                    className="w-full px-4 py-3 bg-dark/70 border border-secondary/20 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent text-light resize-none transition-all duration-300"
                    placeholder="Your message here..."
                  ></textarea>
                </motion.div>
                    {/* Email field */}
                    <motion.div variants={itemVariants} className="relative group">
                      <label 
                        htmlFor="email" 
                        className="block text-light mb-3 font-medium text-lg transition-colors duration-300 group-focus-within:text-secondary"
                      >
                        Your Email *
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          onFocus={() => setFocusedField('email')}
                          onBlur={() => setFocusedField(null)}
                          required
                          className={`w-full px-6 py-4 bg-dark/70 border-2 rounded-xl focus:outline-none text-light transition-all duration-300 text-lg
                            ${errors.email 
                              ? 'border-red-500 focus:border-red-400 focus:ring-2 focus:ring-red-400/20' 
                              : focusedField === 'email' 
                                ? 'border-secondary focus:border-secondary focus:ring-2 focus:ring-secondary/20' 
                                : 'border-secondary/20 hover:border-secondary/40'
                            }`}
                          placeholder="<EMAIL>"
                        />
                        <AnimatePresence>
                          {focusedField === 'email' && (
                            <motion.div
                              initial={{ scale: 0, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              exit={{ scale: 0, opacity: 0 }}
                              className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full"
                            />
                          )}
                        </AnimatePresence>
                      </div>
                      <AnimatePresence>
                        {errors.email && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="mt-2 text-red-400 text-sm flex items-center"
                          >
                            <FaExclamationTriangle className="mr-2" />
                            {errors.email}
                          </motion.p>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  </div>

                <motion.div variants={itemVariants}>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="btn-primary w-full flex justify-center items-center group"
                  >
                    {isSubmitting ? (
                      <span className="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-secondary mr-2"></span>
                    ) : (
                      <FaPaperPlane className="mr-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                    )}
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </button>
                  {/* Subject field */}
                  <motion.div variants={itemVariants} className="relative group">
                    <label 
                      htmlFor="subject" 
                      className="block text-light mb-3 font-medium text-lg transition-colors duration-300 group-focus-within:text-secondary"
                    >
                      Subject *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('subject')}
                        onBlur={() => setFocusedField(null)}
                        required
                        className={`w-full px-6 py-4 bg-dark/70 border-2 rounded-xl focus:outline-none text-light transition-all duration-300 text-lg
                          ${errors.subject 
                            ? 'border-red-500 focus:border-red-400 focus:ring-2 focus:ring-red-400/20' 
                            : focusedField === 'subject' 
                              ? 'border-secondary focus:border-secondary focus:ring-2 focus:ring-secondary/20' 
                              : 'border-secondary/20 hover:border-secondary/40'
                          }`}
                        placeholder="Project Inquiry / Job Opportunity / Collaboration"
                      />
                      <AnimatePresence>
                        {focusedField === 'subject' && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0, opacity: 0 }}
                            className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full"
                          />
                        )}
                      </AnimatePresence>
                    </div>
                    <AnimatePresence>
                      {errors.subject && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="mt-2 text-red-400 text-sm flex items-center"
                        >
                          <FaExclamationTriangle className="mr-2" />
                          {errors.subject}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </motion.div>

                  {submitStatus === 'success' && (
                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-4 text-green-400 text-center font-medium"
                  {/* Message field */}
                  <motion.div variants={itemVariants} className="relative group">
                    <label 
                      htmlFor="message" 
                      className="block text-light mb-3 font-medium text-lg transition-colors duration-300 group-focus-within:text-secondary"
                    >
                      Message sent successfully! I'll get back to you soon.
                    </motion.p>
                  )}
                </motion.div>
              </form>
                      Message *
                    </label>
                    <div className="relative">
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('message')}
                        onBlur={() => setFocusedField(null)}
                        required
                        rows="6"
                        className={`w-full px-6 py-4 bg-dark/70 border-2 rounded-xl focus:outline-none text-light resize-none transition-all duration-300 text-lg
                          ${errors.message 
                            ? 'border-red-500 focus:border-red-400 focus:ring-2 focus:ring-red-400/20' 
                            : focusedField === 'message' 
                              ? 'border-secondary focus:border-secondary focus:ring-2 focus:ring-secondary/20' 
                              : 'border-secondary/20 hover:border-secondary/40'
                          }`}
                        placeholder="Tell me about your project, ideas, or how we can work together..."
                      />
                      <AnimatePresence>
                        {focusedField === 'message' && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0, opacity: 0 }}
                            className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full"
                          />
                        )}
                      </AnimatePresence>
                    </div>
                    <AnimatePresence>
                      {errors.message && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="mt-2 text-red-400 text-sm flex items-center"
                        >
                          <FaExclamationTriangle className="mr-2" />
                          {errors.message}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </motion.div>

                  {/* Enhanced submit button */}
                  <motion.div variants={itemVariants} className="pt-4">
                    <motion.button
                      type="submit"
                      disabled={isSubmitting}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="relative w-full group overflow-hidden rounded-xl bg-gradient-to-r from-secondary via-accent to-secondary bg-size-200 bg-pos-0 hover:bg-pos-100 transition-all duration-500 p-[2px]"
                    >
                      <div className="relative bg-dark/90 rounded-xl px-8 py-5 flex justify-center items-center group-hover:bg-dark/70 transition-all duration-300">
                        <AnimatePresence mode="wait">
                          {isSubmitting ? (
                            <motion.div
                              key="loading"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              className="flex items-center text-lg font-medium"
                            >
                              <FaSpinner className="animate-spin mr-3 text-secondary" />
                              <span className="text-lightest">Sending Message...</span>
                            </motion.div>
                          ) : (
                            <motion.div
                              key="send"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              className="flex items-center text-lg font-medium group"
                            >
                              <motion.div
                                animate={{ 
                                  x: [0, 5, 0],
                                  y: [0, -2, 0]
                                }}
                                transition={{ 
                                  duration: 2, 
                                  repeat: Infinity,
                                  ease: "easeInOut"
                                }}
                              >
                                <FaPaperPlane className="mr-3 text-secondary group-hover:text-accent transition-colors duration-300" />
                              </motion.div>
                              <span className="text-lightest group-hover:text-secondary transition-colors duration-300">
                                Send Message
                              </span>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </motion.button>

                    {/* Status messages */}
                    <AnimatePresence>
                      {submitStatus === 'success' && (
                        <motion.div
                          initial={{ opacity: 0, y: 20, scale: 0.9 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -20, scale: 0.9 }}
                          className="mt-6 p-4 bg-green-500/10 border border-green-500/30 rounded-xl flex items-center"
                        >
                          <FaCheckCircle className="text-green-400 mr-3 text-xl" />
                          <div>
                            <p className="text-green-400 font-medium text-lg">Message sent successfully!</p>
                            <p className="text-green-300/80 text-sm mt-1">I'll get back to you within 24 hours.</p>
                          </div>
                        </motion.div>
                      )}
                      
                      {submitStatus === 'error' && (
                        <motion.div
                          initial={{ opacity: 0, y: 20, scale: 0.9 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -20, scale: 0.9 }}
                          className="mt-6 p-4 bg-red-500/10 border border-red-500/30 rounded-xl flex items-center"
                        >
                          <FaExclamationTriangle className="text-red-400 mr-3 text-xl" />
                          <div>
                            <p className="text-red-400 font-medium text-lg">Please fix the errors above</p>
                            <p className="text-red-300/80 text-sm mt-1">All fields are required and must be valid.</p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                </form>
              </div>
            </motion.div>
          </motion.div>

          {/* Contact Info - 2 columns */}
          {/* Enhanced Contact Info - 5 columns */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            initial={{ opacity: 0, x: 40 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:col-span-2 flex flex-col justify-between"
            transition={{ duration: 0.8, delay: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }}
            className="lg:col-span-5 flex flex-col justify-between space-y-8"
          >
            <div className="glass-card backdrop-blur-md mb-6">
              <h3 className="text-2xl font-futuristic text-lightest mb-6 flex items-center">
                <FaEnvelope className="text-secondary mr-3" />
                Contact Information
              </h3>
            {/* Contact Information Card */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-tertiary/5 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
              <div className="relative glass-card backdrop-blur-xl border border-accent/20 group-hover:border-accent/40 transition-all duration-500 rounded-2xl overflow-hidden">
                <div className="p-8">
                  <div className="flex items-center mb-8">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="p-4 bg-accent/10 rounded-xl mr-4"
                    >
                      <FaGlobe className="text-2xl text-accent" />
                    </motion.div>
                    <div>
                      <h3 className="text-2xl font-futuristic text-lightest">Get In Touch</h3><Thinking>
The user wants me to continue the generation exactly where I left off. I can see the code was cut off in the middle of the Contact Information Card section. I need to continue from where it stopped, which was inside the contact information card structure. I should complete the contact information section, add the availability status card, and finish the component properly.

              <div className="space-y-6">
                <div className="flex items-start group">
                  <div className="bg-dark/50 p-3 rounded-lg mr-4 text-secondary group-hover:text-accent group-hover:scale-110 transition-all duration-300">
                    <FaEnvelope className="text-xl" />
Looking at the existing code, I can see we were in the middle of building an enhanced contact information section with professional styling and animations. I need to continue with the contact details, social links, and availability status, then close all the necessary tags and complete the component.
</Thinking>

                      <p className="text-light/70">Let's start a conversation</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-lightest font-medium mb-1">Email</h4>
                    <a href="mailto:<EMAIL>" className="text-light/70 hover:text-secondary transition-colors duration-300">
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start group">
                  <div className="bg-dark/50 p-3 rounded-lg mr-4 text-secondary group-hover:text-accent group-hover:scale-110 transition-all duration-300">
                    <FaPhoneAlt className="text-xl" />
                  <div className="space-y-6">
                    {/* Email */}
                    <motion.div 
                      className="flex items-start group/item cursor-pointer"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="relative">
                        <div className="bg-dark/50 p-4 rounded-xl mr-5 text-secondary group-hover/item:text-accent group-hover/item:scale-110 transition-all duration-300 border border-secondary/20 group-hover/item:border-accent/40">
                          <FaEnvelope className="text-xl" />
                        </div>
                        <motion.div
                          className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lightest font-semibold mb-2 text-lg group-hover/item:text-secondary transition-colors duration-300">Email</h4>
                        <a 
                          href="mailto:<EMAIL>" 
                          className="text-light/80 hover:text-secondary transition-colors duration-300 text-base break-all"
                        >
                          <EMAIL>
                        </a>
                      </div>
                    </motion.div>

                    {/* Phone */}
                    <motion.div 
                      className="flex items-start group/item cursor-pointer"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="relative">
                        <div className="bg-dark/50 p-4 rounded-xl mr-5 text-secondary group-hover/item:text-accent group-hover/item:scale-110 transition-all duration-300 border border-secondary/20 group-hover/item:border-accent/40">
                          <FaPhoneAlt className="text-xl" />
                        </div>
                        <motion.div
                          className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lightest font-semibold mb-2 text-lg group-hover/item:text-secondary transition-colors duration-300">Phone</h4>
                        <a 
                          href="tel:+918186086478" 
                          className="text-light/80 hover:text-secondary transition-colors duration-300 text-base"
                        >
                          +91 8186086478
                        </a>
                      </div>
                    </motion.div>

                    {/* LinkedIn */}
                    <motion.div 
                      className="flex items-start group/item cursor-pointer"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="relative">
                        <div className="bg-dark/50 p-4 rounded-xl mr-5 text-secondary group-hover/item:text-accent group-hover/item:scale-110 transition-all duration-300 border border-secondary/20 group-hover/item:border-accent/40">
                          <FaLinkedin className="text-xl" />
                        </div>
                        <motion.div
                          className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lightest font-semibold mb-2 text-lg group-hover/item:text-secondary transition-colors duration-300">LinkedIn</h4>
                        <a 
                          href="https://linkedin.com/in/karra-chandra-sekhar" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-light/80 hover:text-secondary transition-colors duration-300 text-base break-all"
                        >
                          linkedin.com/in/karra-chandra-sekhar
                        </a>
                      </div>
                    </motion.div>

                    {/* GitHub */}
                    <motion.div 
                      className="flex items-start group/item cursor-pointer"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="relative">
                        <div className="bg-dark/50 p-4 rounded-xl mr-5 text-secondary group-hover/item:text-accent group-hover/item:scale-110 transition-all duration-300 border border-secondary/20 group-hover/item:border-accent/40">
                          <FaGithub className="text-xl" />
                        </div>
                        <motion.div
                          className="absolute -top-1 -right-1 w-3 h-3 bg-secondary rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: 1.5 }}
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lightest font-semibold mb-2 text-lg group-hover/item:text-secondary transition-colors duration-300">GitHub</h4>
                        <a 
                          href="https://github.com/karrachandrasekhar" 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-light/80 hover:text-secondary transition-colors duration-300 text-base break-all"
                        >
                          github.com/karrachandrasekhar
                        </a>
                      </div>
                    </motion.div>
                  </div>
                  <div>
                    <h4 className="text-lightest font-medium mb-1">Phone</h4>
                    <a href="tel:+918186086478" className="text-light/70 hover:text-secondary transition-colors duration-300">
                      +91 8186086478
                    </a>
                  </div>
                </div>
              </div>
            </div>

                <div className="flex items-start group">
                  <div className="bg-dark/50 p-3 rounded-lg mr-4 text-secondary group-hover:text-accent group-hover:scale-110 transition-all duration-300">
                    <FaLinkedin className="text-xl" />
            {/* Response Time Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-tertiary/5 via-transparent to-secondary/5 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
              <div className="relative glass-card backdrop-blur-xl border border-tertiary/20 group-hover:border-tertiary/40 transition-all duration-500 rounded-2xl overflow-hidden">
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                      className="w-3 h-3 rounded-full bg-green-400 mr-3"
                    />
                    <h4 className="text-lightest font-bold text-lg">Quick Response Time</h4>
                  </div>
                  <div>
                    <h4 className="text-lightest font-medium mb-1">LinkedIn</h4>
                    <a href="https://linkedin.com/in/karra-chandra-sekhar" target="_blank" rel="noopener noreferrer" className="text-light/70 hover:text-secondary transition-colors duration-300">
                      linkedin.com/in/karra-chandra-sekhar
                    </a>
                  <div className="space-y-3">
                    <div className="flex items-center text-light/80">
                      <FaClock className="text-tertiary mr-3" />
                      <span className="text-sm">Usually responds within 2-4 hours</span>
                    </div>
                    <div className="flex items-center text-light/80">
                      <FaMapMarkerAlt className="text-tertiary mr-3" />
                      <span className="text-sm">Based in India (IST timezone)</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

                <div className="flex items-start group">
                  <div className="bg-dark/50 p-3 rounded-lg mr-4 text-secondary group-hover:text-accent group-hover:scale-110 transition-all duration-300">
                    <FaGithub className="text-xl" />
            {/* Availability Status Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-highlight/5 via-transparent to-accent/5 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
              <div className="relative glass-card backdrop-blur-xl border border-highlight/20 group-hover:border-highlight/40 transition-all duration-500 rounded-2xl overflow-hidden">
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <motion.div
                      animate={{ 
                        scale: [1, 1.2, 1],
                        opacity: [0.7, 1, 0.7]
                      }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="w-3 h-3 rounded-full bg-green-400 mr-3"
                    />
                    <h4 className="text-lightest font-bold text-lg">Available for Work</h4>
                  </div>
                  <div>
                    <h4 className="text-lightest font-medium mb-1">GitHub</h4>
                    <a href="https://github.com/karrachandrasekhar" target="_blank" rel="noopener noreferrer" className="text-light/70 hover:text-secondary transition-colors duration-300">
                      github.com/karrachandrasekhar
                    </a>
                  <p className="text-light/80 text-sm leading-relaxed mb-4">
                    I'm currently open to new opportunities in software development, 
                    freelance projects, and exciting collaborations.
                  </p>
                  <div className="flex items-center text-highlight">
                    <FaHeart className="mr-2 text-sm" />
                    <span className="text-sm font-medium">Open to remote work</span>
                  </div>
                </div>
              </div>
            </div>
            </motion.div>

            {/* Fun Fact Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="glass-card backdrop-blur-md border-accent/20 hover:border-accent/40 transition-all duration-300"
              transition={{ duration: 0.6, delay: 0.7 }}
              className="relative group"
            >
              <div className="flex items-center mb-4">
                <div className="w-2 h-2 rounded-full bg-green-400 mr-2 animate-pulse"></div>
                <h4 className="text-lightest font-bold">Available for Opportunities</h4>
              <div className="absolute inset-0 bg-gradient-to-br from-secondary/5 via-transparent to-highlight/5 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
              <div className="relative glass-card backdrop-blur-xl border border-secondary/20 group-hover:border-secondary/40 transition-all duration-500 rounded-2xl overflow-hidden">
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <motion.div
                      animate={{ rotate: [0, 15, -15, 0] }}
                      transition={{ duration: 4, repeat: Infinity }}
                      className="text-2xl mr-3"
                    >
                      💡
                    </motion.div>
                    <h4 className="text-lightest font-bold text-lg">Fun Fact</h4>
                  </div>
                  <p className="text-light/80 text-sm leading-relaxed">
                    I believe the best code is written with coffee in hand and good music in the background. 
                    Let's create something amazing together! ☕🎵
                  </p>
                </div>
              </div>
              <p className="text-light/80">
                I'm currently looking for new opportunities in software development.
                If you have a position that matches my skills and experience, please feel free to reach out!
              </p>
            </motion.div>
          </motion.div>
        </div>

        {/* Bottom CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20 text-center"
        >
          <div className="relative inline-block">
            <motion.div
              animate={{ 
                rotate: [0, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 10, 
                repeat: Infinity,
                ease: "linear"
              }}
              className="absolute -inset-4 bg-gradient-to-r from-secondary via-accent to-highlight rounded-full opacity-20 blur-lg"
            />
            <div className="relative bg-dark/80 backdrop-blur-xl rounded-2xl p-8 border border-secondary/20">
              <h3 className="text-2xl font-futuristic text-lightest mb-4">
                Ready to Start Something Great?
              </h3>
              <p className="text-light/80 mb-6 max-w-md mx-auto">
                Whether it's a new project, collaboration, or just a friendly chat about technology, 
                I'm always excited to connect with fellow creators and innovators.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <motion.a
                  href="mailto:<EMAIL>"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-secondary to-accent rounded-lg text-dark font-medium hover:shadow-neon transition-all duration-300"
                >
                  <FaEnvelope className="mr-2" />
                  Quick Email
                </motion.a>
                <motion.a
                  href="https://linkedin.com/in/karra-chandra-sekhar"
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center px-6 py-3 bg-dark/50 border border-secondary/40 rounded-lg text-lightest font-medium hover:border-secondary hover:shadow-neon-accent transition-all duration-300"
                >
                  <FaLinkedin className="mr-2" />
                  LinkedIn
                </motion.a>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
