import { motion } from 'framer-motion';

const About = () => {
  return (
    <section id="about" className="py-20 bg-primary">
      <div className="section-container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="section-title">
            <span className="text-secondary font-mono">01.</span> About Me
          </h2>
        </motion.div>

        <div className="grid md:grid-cols-5 gap-10 mt-10">
          <motion.div
            className="md:col-span-3"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="space-y-4 text-light/80">
              <p>
                Hello! I'm <PERSON><PERSON>, a recent graduate with a B.Tech in Computer Science and Business Systems.
                My passion for software development started back in 2019, when I first learned about web development and was fascinated by how code can transform ideas into interactive experiences.
              </p>

              <p>
                Throughout my academic journey, I've developed a strong foundation in computer science principles,
                software engineering practices, and business systems. I enjoy solving complex problems and creating
                efficient, user-friendly applications that make a real impact.
              </p>

              <p>
                My experience includes internships at tech companies where I worked on web development projects and database management systems.
                I'm particularly interested in full-stack web development, cloud computing, and data analytics.
              </p>

              <p>
                When I'm not coding, you can find me playing chess, reading tech blogs, and exploring photography.
                I believe that diverse experiences contribute to creative problem-solving in technology.
              </p>

              <div className="pt-2">
                <p className="mb-2">Here are a few technologies I've been working with recently:</p>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> JavaScript (ES6+)
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> React
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> Node.js
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> Python
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> Java
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> SQL
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> MongoDB
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> AWS
                  </div>
                  <div className="flex items-center">
                    <span className="text-secondary mr-2">▹</span> Git
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="md:col-span-2 flex justify-center md:justify-end"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="relative group">
              <div className="absolute -inset-0.5 bg-secondary rounded-md blur opacity-25 group-hover:opacity-50 transition duration-300"></div>
              <div className="relative max-w-xs">
                <div className="w-full h-full absolute bg-secondary/20 rounded-md translate-x-3 translate-y-3 transition-all duration-300 group-hover:translate-x-5 group-hover:translate-y-5"></div>
                <div className="relative rounded-md overflow-hidden border-2 border-secondary">
                  <img
                    src="/images/profile.jpg"
                    alt="Karra Chandra Sekhar"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default About;
