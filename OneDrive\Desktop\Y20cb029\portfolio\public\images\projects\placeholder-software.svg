<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9a3412;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="800" height="400" fill="url(#grad1)" />
  <circle cx="200" cy="200" r="80" fill="#ffffff" opacity="0.1" />
  <circle cx="600" cy="200" r="100" fill="#ffffff" opacity="0.1" />
  <text x="400" y="180" font-family="Arial" font-size="40" fill="white" text-anchor="middle">Software Development</text>
  <text x="400" y="230" font-family="Arial" font-size="20" fill="white" text-anchor="middle">Java, Python & C++</text>
</svg>
