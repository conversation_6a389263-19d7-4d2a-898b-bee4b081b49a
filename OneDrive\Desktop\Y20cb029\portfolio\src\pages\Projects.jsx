import { useState, useEffect } from 'react';

const Projects = () => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoaded, setIsLoaded] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);

  useEffect(() => {
    // Set loaded state after a small delay for animations
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Close popup when escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        setSelectedProject(null);
      }
    };

    window.addEventListener('keydown', handleEscKey);

    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, []);

  // Prevent body scrolling when popup is open
  useEffect(() => {
    if (selectedProject) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [selectedProject]);

  // GitHub repositories data based on your actual GitHub profile
  const projects = [
    {
      title: "Sports Portfolio",
      description: "A responsive sports portfolio website showcasing athletic achievements, statistics, and career highlights with modern CSS animations and effects.",
      technologies: ["HTML", "CSS", "JavaScript", "Responsive Design"],
      image: "/images/projects/placeholder-web.svg",
      github: "https://github.com/karravulachandra/sports-portfolio",
      demo: "https://github.com/karravulachandra/sports-portfolio",
      category: "web",
      longDescription: "A comprehensive sports portfolio website designed to showcase athletic achievements, career statistics, and professional highlights. Features include a responsive design that works seamlessly across all devices, modern CSS animations for engaging user experience, interactive galleries for media content, and a performance statistics dashboard with data visualization."
    },
    {
      title: "Personal Portfolio Showcase",
      description: "A professional portfolio website template with modern design elements, animations, and responsive layouts for showcasing personal projects and skills.",
      technologies: ["HTML", "CSS", "JavaScript", "React"],
      image: "/images/projects/placeholder-web.svg",
      github: "https://github.com/karravulachandra/PersonalPortfolioShowcase",
      demo: "https://github.com/karravulachandra/PersonalPortfolioShowcase",
      category: "web",
      longDescription: "A highly customizable portfolio website template built with React, designed for professionals to showcase their work and skills. Features include a modular component structure for easy customization, smooth page transitions and animations, dark/light theme options, filterable project galleries, and integration with various APIs for dynamic content display."
    },
    {
      title: "Chat Checkpoints",
      description: "A real-time chat application with checkpoint functionality allowing users to save and return to specific points in conversation history.",
      technologies: ["TypeScript", "React", "Node.js", "WebSockets"],
      image: "/images/projects/placeholder-web.svg",
      github: "https://github.com/karravulachandra/chat-checkpoints",
      demo: "https://github.com/karravulachandra/chat-checkpoints",
      category: "web",
      longDescription: "An innovative real-time chat application that introduces the concept of conversation checkpoints. Users can mark important points in their chat history and easily navigate back to them later. Built with TypeScript for type safety, React for the frontend interface, Node.js for the backend server, and WebSockets for real-time communication. Features include end-to-end encryption, message search functionality, media sharing capabilities, and user presence indicators."
    },
    {
      title: "IndApp",
      description: "A mobile application for connecting independent service providers with customers, featuring booking system, reviews, and payment processing.",
      technologies: ["React Native", "Firebase", "Node.js", "Express"],
      image: "/images/projects/placeholder-mobile.svg",
      github: "https://github.com/karravulachandra/Indapp",
      demo: "https://github.com/karravulachandra/Indapp",
      category: "mobile",
      longDescription: "A comprehensive mobile platform that bridges the gap between independent service providers and customers. Built with React Native for cross-platform compatibility, Firebase for real-time database and authentication, and Node.js/Express for the backend API. Key features include a sophisticated booking system with availability calendar, secure payment processing with multiple options, verified review system with photo uploads, service provider profiles with portfolio showcases, and in-app messaging for direct communication."
    },
    {
      title: "GCP Project",
      description: "A cloud-based application deployed on Google Cloud Platform, demonstrating scalable architecture and cloud-native development practices.",
      technologies: ["Google Cloud", "Docker", "Kubernetes", "Python"],
      image: "/images/projects/placeholder-cloud.svg",
      github: "https://github.com/karravulachandra/GCP_Project",
      demo: "https://github.com/karravulachandra/GCP_Project",
      category: "cloud",
      longDescription: "A comprehensive demonstration of cloud-native application development and deployment on Google Cloud Platform. This project showcases best practices for building scalable, resilient, and maintainable cloud applications. Implemented with containerization using Docker, orchestration with Kubernetes for automated deployment and scaling, and Python for application logic. Features include auto-scaling based on traffic patterns, distributed logging and monitoring, CI/CD pipeline integration, and disaster recovery strategies."
    },
    {
      title: "Web Development",
      description: "A collection of web development projects showcasing various frontend and backend technologies, design patterns, and best practices.",
      technologies: ["JavaScript", "HTML", "CSS", "Node.js", "Express"],
      image: "/images/projects/placeholder-web.svg",
      github: "https://github.com/karravulachandra/web-development",
      demo: "https://github.com/karravulachandra/web-development",
      category: "web",
      longDescription: "A comprehensive repository of web development projects demonstrating proficiency in both frontend and backend technologies. This collection includes responsive websites, single-page applications, RESTful APIs, and full-stack applications. Each project follows industry best practices for code organization, performance optimization, accessibility, and security. Technologies used include modern JavaScript (ES6+), HTML5, CSS3 with preprocessors, Node.js with Express for backend services, and various databases for data persistence."
    },
    {
      title: "VCare",
      description: "A healthcare management system designed to streamline patient care, appointment scheduling, and medical record management.",
      technologies: ["Java", "Spring Boot", "MySQL", "React"],
      image: "/images/projects/placeholder-web.svg",
      github: "https://github.com/karravulachandra/vcare",
      demo: "https://github.com/karravulachandra/vcare",
      category: "web",
      longDescription: "A comprehensive healthcare management system built to modernize and streamline medical practice operations. VCare provides an integrated solution for patient management, appointment scheduling, electronic medical records, billing, and reporting. The backend is developed with Java and Spring Boot, providing a robust and secure API, while the frontend uses React for a responsive and intuitive user interface. Data is stored in a MySQL database with proper normalization and indexing for optimal performance. Features include role-based access control, audit logging for compliance, automated appointment reminders, and comprehensive reporting capabilities."
    },
    {
      title: "Village Agency",
      description: "A platform connecting rural communities with government services, resources, and development programs to bridge the urban-rural divide.",
      technologies: ["JavaScript", "Node.js", "MongoDB", "Express"],
      image: "/images/projects/placeholder-web.svg",
      github: "https://github.com/karravulachandra/VillageAgency.1.0.0-",
      demo: "https://github.com/karravulachandra/VillageAgency.1.0.0-",
      category: "web",
      longDescription: "An innovative platform designed to bridge the digital divide between rural communities and government services. Village Agency serves as a centralized hub where rural residents can access information about government schemes, apply for benefits, register complaints, and track application status. The platform is built with Node.js and Express for the backend, MongoDB for flexible data storage, and JavaScript for the frontend. Key features include multilingual support for regional languages, offline capability for areas with limited connectivity, simplified user interfaces designed for users with limited digital literacy, and a dashboard for government officials to monitor program implementation and gather analytics."
    },
    {
      title: "Java Project",
      description: "A comprehensive Java application demonstrating object-oriented programming principles, design patterns, and Java best practices.",
      technologies: ["Java", "OOP", "Design Patterns", "JUnit"],
      image: "/images/projects/placeholder-software.svg",
      github: "https://github.com/karravulachandra/Java-Project",
      demo: "https://github.com/karravulachandra/Java-Project",
      category: "software",
      longDescription: "A sophisticated Java application that serves as a showcase for advanced object-oriented programming concepts and design patterns. This project demonstrates clean code principles, SOLID design principles, and effective use of Java features. The application implements multiple design patterns including Factory, Singleton, Observer, Strategy, and Decorator patterns in practical contexts. It includes comprehensive unit tests with JUnit, demonstrating test-driven development practices. The project also showcases proper exception handling, logging, multithreading, and resource management in Java applications."
    },
    {
      title: "MLOps Fundamentals",
      description: "A project exploring MLOps practices including model versioning, automated testing, continuous integration, and deployment for machine learning models.",
      technologies: ["Python", "TensorFlow", "Docker", "CI/CD", "Kubernetes"],
      image: "/images/projects/placeholder-ai.svg",
      github: "https://github.com/karravulachandra/Fundamentals-of-MLOps",
      demo: "https://github.com/karravulachandra/Fundamentals-of-MLOps",
      category: "ai",
      longDescription: "A comprehensive demonstration of MLOps (Machine Learning Operations) practices for the entire machine learning lifecycle. This project showcases the infrastructure and workflows needed to reliably deploy and maintain machine learning models in production. Key components include automated data validation and preprocessing pipelines, model versioning and experiment tracking, automated model testing and validation, containerization of ML models with Docker, continuous integration and deployment pipelines, model serving with monitoring and logging, and automated retraining workflows. The project uses TensorFlow for model development, Kubernetes for orchestration, and various CI/CD tools for automation."
    },
    {
      title: "Java Lab Exercise",
      description: "A collection of Java programming exercises covering fundamental concepts, data structures, algorithms, and advanced Java features.",
      technologies: ["Java", "Data Structures", "Algorithms"],
      image: "/images/projects/placeholder-software.svg",
      github: "https://github.com/karravulachandra/javalabexercise",
      demo: "https://github.com/karravulachandra/javalabexercise",
      category: "software",
      longDescription: "A comprehensive collection of Java programming exercises designed to build proficiency in Java programming from fundamentals to advanced concepts. The exercises are organized into progressive modules covering core Java syntax, object-oriented programming principles, data structures (arrays, linked lists, stacks, queues, trees, graphs), algorithms (sorting, searching, dynamic programming), file I/O operations, multithreading, Java collections framework, exception handling, and Java 8+ features including lambdas and streams. Each exercise includes problem statements, solution implementations, and unit tests to verify correctness."
    },
    {
      title: "Malware Detection with ML",
      description: "An advanced malware detection system using machine learning and deep learning techniques to identify and classify malicious software.",
      technologies: ["Python", "Machine Learning", "Deep Learning", "Cybersecurity"],
      image: "/images/projects/placeholder-ai.svg",
      github: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      demo: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      category: "ai",
      longDescription: "A sophisticated malware detection system that leverages machine learning and deep learning techniques to identify and classify malicious software with high accuracy. The system analyzes both static features (file metadata, headers, strings) and dynamic features (behavior patterns, API calls, network activity) to detect known and zero-day malware threats. Multiple models are implemented and compared, including traditional machine learning algorithms (Random Forests, SVMs, Gradient Boosting) and deep learning approaches (CNNs, RNNs, and transformer-based models). The system includes a comprehensive feature engineering pipeline, model training and evaluation framework, and a deployable detection service with explainable AI components to help security analysts understand detection decisions."
    }
  ];

  // Filter projects based on category and search term
  const filteredProjects = projects.filter(project => {
    const matchesCategory = filter === 'all' || project.category === filter;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Categories for filter
  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'web', name: 'Web Development' },
    { id: 'mobile', name: 'Mobile Apps' },
    { id: 'ai', name: 'AI & ML' },
    { id: 'cloud', name: 'Cloud Computing' },
    { id: 'software', name: 'Software Development' }
  ];

  return (
    <div className="py-10">
      {/* Header */}
      <div className="mb-10 text-center">
        <h1 className="text-4xl font-bold mb-4 text-white">
          My Projects
        </h1>
        <div className="h-1 w-20 bg-indigo-500 mx-auto mb-6"></div>
        <p className="text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed">
          Explore my portfolio of projects showcasing my skills in web development, mobile applications,
          AI/ML, and more. Each project represents different technologies and problem-solving approaches.
        </p>
      </div>

      {/* GitHub profile link */}
      <div className="flex justify-center mb-10">
        <a
          href="https://github.com/karravulachandra"
          target="_blank"
          rel="noopener noreferrer"
          className="btn btn-outline inline-flex items-center"
        >
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
          View GitHub Profile
        </a>
      </div>

      {/* Filters and Search */}
      <div className="mb-10 card p-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* Category filters */}
          <div className="flex flex-wrap gap-2 justify-center md:justify-start">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setFilter(category.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-300 ${
                  filter === category.id
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Search input */}
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 text-white"
            />
            <svg
              className="absolute right-3 top-2.5 w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Projects grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.length > 0 ? (
          filteredProjects.map((project, index) => (
            <ProjectCard
              key={index}
              project={project}
              delay={index * 100}
              isLoaded={isLoaded}
              index={index}
              onClick={setSelectedProject}
            />
          ))
        ) : (
          <div className="col-span-full text-center py-10">
            <p className="text-gray-400 text-lg">No projects match your search criteria. Try adjusting your filters.</p>
          </div>
        )}
      </div>

      {/* Project Details Popup */}
      {selectedProject && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 animate-fadeIn">
          <div
            className="relative bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-white z-10 bg-gray-800 rounded-full p-2"
              onClick={() => setSelectedProject(null)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Project image */}
            <div className="relative h-64 md:h-80">
              <img
                src={selectedProject.image}
                alt={selectedProject.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  // Try project-specific SVG placeholder if JPG is not available
                  const projectName = selectedProject.title.toLowerCase().replace(/\s+/g, '-');
                  e.target.src = `/images/projects/${projectName}.svg`;
                  // If project-specific SVG fails, try category placeholder
                  e.target.onerror = () => {
                    e.target.onerror = null;
                    e.target.src = `/images/projects/placeholder-${selectedProject.category}.svg`;
                    // If category SVG also fails, use a data URI as final fallback
                    e.target.onerror = () => {
                      e.target.onerror = null;
                      e.target.src = "data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='400' viewBox='0 0 800 400'%3E%3Crect fill='%23343a40' width='800' height='400'/%3E%3Ctext fill='rgba(255,255,255,0.7)' font-family='Arial' font-size='36' text-anchor='middle' x='400' y='200'%3E" + selectedProject.title + "%3C/text%3E%3C/svg%3E";
                    };
                  };
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>

              {/* Category badge */}
              <div className="absolute top-4 left-4">
                <div className="px-3 py-1 rounded-md text-sm font-medium bg-indigo-600 text-white">
                  {selectedProject.category.charAt(0).toUpperCase() + selectedProject.category.slice(1)}
                </div>
              </div>
            </div>

            {/* Project content */}
            <div className="p-6">
              <h2 className="text-2xl font-bold text-white mb-4">{selectedProject.title}</h2>

              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-200 mb-2">Description</h3>
                <p className="text-gray-300 leading-relaxed">
                  {selectedProject.longDescription || selectedProject.description}
                </p>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-200 mb-2">Technologies</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedProject.technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gray-800 text-sm rounded-md text-gray-300 border border-gray-700"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Project links */}
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href={selectedProject.github}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-secondary flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                  View on GitHub
                </a>
                <a
                  href={selectedProject.demo}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-primary flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  View Live Demo
                </a>
              </div>
            </div>
          </div>

          {/* Background overlay click handler */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => setSelectedProject(null)}
          ></div>
        </div>
      )}
    </div>
  );
};

const ProjectCard = ({ project, delay, isLoaded, onClick }) => {
  // Get category icon
  const CategoryIcon = () => {
    switch(project.category) {
      case 'web':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
          </svg>
        );
      case 'mobile':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        );
      case 'ai':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case 'cloud':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
          </svg>
        );
      case 'software':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
          </svg>
        );
    }
  };

  return (
    <div
      className={`card card-hover h-full flex flex-col animate-fadeIn cursor-pointer ${
        isLoaded ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
      onClick={() => onClick(project)}
    >
      {/* Category badge */}
      <div className="absolute top-3 right-3 z-20">
        <div className="flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium bg-gray-800 text-indigo-400 border border-gray-700">
          <CategoryIcon />
          <span>{project.category.charAt(0).toUpperCase() + project.category.slice(1)}</span>
        </div>
      </div>

      {/* Image */}
      <div className="relative overflow-hidden h-48">
        <img
          src={project.image}
          alt={project.title}
          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          onError={(e) => {
            e.target.onerror = null;
            // Try project-specific SVG placeholder if JPG is not available
            const projectName = project.title.toLowerCase().replace(/\s+/g, '-');
            e.target.src = `/images/projects/${projectName}.svg`;
            // If project-specific SVG fails, try category placeholder
            e.target.onerror = () => {
              e.target.onerror = null;
              e.target.src = `/images/projects/placeholder-${project.category}.svg`;
              // If category SVG also fails, use a data URI as final fallback
              e.target.onerror = () => {
                e.target.onerror = null;
                e.target.src = "data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='400' viewBox='0 0 800 400'%3E%3Crect fill='%23343a40' width='800' height='400'/%3E%3Ctext fill='rgba(255,255,255,0.7)' font-family='Arial' font-size='36' text-anchor='middle' x='400' y='200'%3E" + project.title + "%3C/text%3E%3C/svg%3E";
              };
            };
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/90 via-gray-900/50 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="p-5 flex-grow">
        <h3 className="text-lg font-medium text-white mb-2">{project.title}</h3>

        <p className="text-gray-400 mb-4 line-clamp-3">
          {project.description}
        </p>

        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-300 mb-2">Technologies:</h4>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-800 text-xs rounded-md text-gray-300 border border-gray-700"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="p-5 pt-0 mt-auto">
        <div className="flex space-x-3">
          <a
            href={project.github}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 btn btn-secondary text-sm flex items-center justify-center"
            onClick={(e) => e.stopPropagation()} // Prevent card click when clicking on link
          >
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            GitHub
          </a>
          <a
            href={project.demo}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 btn btn-primary text-sm flex items-center justify-center"
            onClick={(e) => e.stopPropagation()} // Prevent card click when clicking on link
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            Demo
          </a>
        </div>
      </div>

      {/* View details indicator */}
      <div className="absolute bottom-3 left-3 text-xs text-gray-400 flex items-center">
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        View Details
      </div>
    </div>
  );
};

export default Projects;
