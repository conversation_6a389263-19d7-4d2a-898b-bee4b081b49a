import { useState, useEffect } from 'react';

const Projects = () => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState(null);

  // Close popup when escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        setSelectedProject(null);
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, []);

  // Prevent body scrolling when popup is open
  useEffect(() => {
    if (selectedProject) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [selectedProject]);

  // Featured projects data
  const projects = [
    {
      id: 1,
      title: "Malware Detection with ML",
      description: "Advanced malware detection system using machine learning and deep learning techniques to identify and classify malicious software with high accuracy.",
      technologies: ["Python", "TensorFlow", "PyTorch", "Scikit-learn", "Computer Vision"],
      image: "/images/projects/malware-detection.jpg",
      github: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      demo: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      category: "ai",
      featured: true,
      status: "Completed",
      year: "2024",
      longDescription: "A sophisticated malware detection system that leverages machine learning and deep learning techniques to identify and classify malicious software with high accuracy. The system analyzes both static features (file metadata, headers, strings) and dynamic features (behavior patterns, API calls, network activity) to detect known and zero-day malware threats. Multiple models are implemented and compared, including traditional machine learning algorithms and deep learning approaches.",
      highlights: [
        "95% accuracy in malware detection",
        "Real-time threat analysis",
        "Multiple ML model comparison",
        "Explainable AI components"
      ]
    },
    {
      id: 2,
      title: "MLOps Fundamentals",
      description: "Comprehensive MLOps project showcasing model versioning, automated testing, CI/CD pipelines, and deployment strategies for machine learning models.",
      technologies: ["Python", "TensorFlow", "Docker", "Kubernetes", "CI/CD"],
      image: "/images/projects/mlops.jpg",
      github: "https://github.com/karravulachandra/Fundamentals-of-MLOps",
      demo: "https://github.com/karravulachandra/Fundamentals-of-MLOps",
      category: "ai",
      featured: true,
      status: "Completed",
      year: "2024",
      longDescription: "A comprehensive demonstration of MLOps practices for the entire machine learning lifecycle. This project showcases the infrastructure and workflows needed to reliably deploy and maintain machine learning models in production. Includes automated data validation, model versioning, containerization, and monitoring.",
      highlights: [
        "End-to-end ML pipeline automation",
        "Model versioning and experiment tracking",
        "Containerized deployment with Docker",
        "Kubernetes orchestration"
      ]
    },
    {
      id: 3,
      title: "VCare Healthcare System",
      description: "Comprehensive healthcare management system for patient care, appointment scheduling, and medical record management with modern web technologies.",
      technologies: ["Java", "Spring Boot", "React", "MySQL", "REST APIs"],
      image: "/images/projects/vcare.jpg",
      github: "https://github.com/karravulachandra/vcare",
      demo: "https://github.com/karravulachandra/vcare",
      category: "web",
      featured: true,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive healthcare management system built to modernize medical practice operations. VCare provides an integrated solution for patient management, appointment scheduling, electronic medical records, billing, and reporting. Features role-based access control, audit logging, and automated appointment reminders.",
      highlights: [
        "Complete patient management system",
        "Secure medical records handling",
        "Automated appointment scheduling",
        "Role-based access control"
      ]
    },
    {
      id: 4,
      title: "IndApp - Service Provider Platform",
      description: "Mobile application connecting independent service providers with customers, featuring booking system, reviews, and secure payment processing.",
      technologies: ["React Native", "Firebase", "Node.js", "Express", "Payment APIs"],
      image: "/images/projects/indapp.jpg",
      github: "https://github.com/karravulachandra/Indapp",
      demo: "https://github.com/karravulachandra/Indapp",
      category: "mobile",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive mobile platform that bridges the gap between independent service providers and customers. Built with React Native for cross-platform compatibility, Firebase for real-time database and authentication, and Node.js/Express for the backend API.",
      highlights: [
        "Cross-platform mobile app",
        "Real-time booking system",
        "Secure payment integration",
        "Review and rating system"
      ]
    },
    {
      id: 5,
      title: "GCP Cloud Architecture",
      description: "Scalable cloud application demonstrating Google Cloud Platform best practices, containerization, and microservices architecture.",
      technologies: ["Google Cloud", "Docker", "Kubernetes", "Python", "Microservices"],
      image: "/images/projects/gcp-project.jpg",
      github: "https://github.com/karravulachandra/GCP_Project",
      demo: "https://github.com/karravulachandra/GCP_Project",
      category: "cloud",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive demonstration of cloud-native application development and deployment on Google Cloud Platform. This project showcases best practices for building scalable, resilient, and maintainable cloud applications.",
      highlights: [
        "Kubernetes orchestration",
        "Auto-scaling implementation",
        "CI/CD pipeline integration",
        "Monitoring and logging"
      ]
    },
    {
      id: 6,
      title: "Web Development Portfolio",
      description: "Collection of modern web applications showcasing frontend and backend technologies, responsive design, and best practices.",
      technologies: ["JavaScript", "React", "Node.js", "Express", "MongoDB"],
      image: "/images/projects/web-development.jpg",
      github: "https://github.com/karravulachandra/web-development",
      demo: "https://github.com/karravulachandra/web-development",
      category: "web",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive repository of web development projects demonstrating proficiency in both frontend and backend technologies. This collection includes responsive websites, single-page applications, RESTful APIs, and full-stack applications.",
      highlights: [
        "Responsive design patterns",
        "RESTful API development",
        "Modern JavaScript (ES6+)",
        "Performance optimization"
      ]
    },
    {
      id: 7,
      title: "Village Agency Platform",
      description: "Digital platform connecting rural communities with government services, resources, and development programs to bridge the urban-rural divide.",
      technologies: ["JavaScript", "Node.js", "MongoDB", "Express", "Progressive Web App"],
      image: "/images/projects/village-agency.jpg",
      github: "https://github.com/karravulachandra/VillageAgency.1.0.0-",
      demo: "https://github.com/karravulachandra/VillageAgency.1.0.0-",
      category: "web",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "An innovative platform designed to bridge the digital divide between rural communities and government services. Village Agency serves as a centralized hub where rural residents can access information about government schemes and apply for benefits.",
      highlights: [
        "Government services integration",
        "Multilingual support",
        "Offline capability",
        "User-friendly interface"
      ]
    },
  ];

  // Project categories for filtering
  const categories = [
    { id: 'all', name: 'All Projects', count: projects.length },
    { id: 'ai', name: 'AI & ML', count: projects.filter(p => p.category === 'ai').length },
    { id: 'web', name: 'Web Development', count: projects.filter(p => p.category === 'web').length },
    { id: 'mobile', name: 'Mobile Apps', count: projects.filter(p => p.category === 'mobile').length },
    { id: 'cloud', name: 'Cloud & DevOps', count: projects.filter(p => p.category === 'cloud').length }
  ];
    {
      title: "Malware Detection with ML",
      description: "An advanced malware detection system using machine learning and deep learning techniques to identify and classify malicious software.",
      technologies: ["Python", "Machine Learning", "Deep Learning", "Cybersecurity"],
      image: "/images/projects/placeholder-ai.svg",
      github: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      demo: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      category: "ai",
      longDescription: "A sophisticated malware detection system that leverages machine learning and deep learning techniques to identify and classify malicious software with high accuracy. The system analyzes both static features (file metadata, headers, strings) and dynamic features (behavior patterns, API calls, network activity) to detect known and zero-day malware threats. Multiple models are implemented and compared, including traditional machine learning algorithms (Random Forests, SVMs, Gradient Boosting) and deep learning approaches (CNNs, RNNs, and transformer-based models). The system includes a comprehensive feature engineering pipeline, model training and evaluation framework, and a deployable detection service with explainable AI components to help security analysts understand detection decisions."
    }
  ];

  // Filter projects based on category and search term
  const filteredProjects = projects.filter(project => {
    const matchesCategory = filter === 'all' || project.category === filter;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Categories for filter
  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'web', name: 'Web Development' },
    { id: 'mobile', name: 'Mobile Apps' },
    { id: 'ai', name: 'AI & ML' },
    { id: 'cloud', name: 'Cloud Computing' },
    { id: 'software', name: 'Software Development' }
  ];

  return (
    <div className="py-10">
      {/* Header */}
      <div className="mb-10 text-center">
        <h1 className="text-4xl font-bold mb-4 text-white">
          My Projects
        </h1>
        <div className="h-1 w-20 bg-indigo-500 mx-auto mb-6"></div>
        <p className="text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed">
          Explore my portfolio of projects showcasing my skills in web development, mobile applications,
          AI/ML, and more. Each project represents different technologies and problem-solving approaches.
        </p>
      </div>

      {/* GitHub profile link */}
      <div className="flex justify-center mb-10">
        <a
          href="https://github.com/karravulachandra"
          target="_blank"
          rel="noopener noreferrer"
          className="btn btn-outline inline-flex items-center"
        >
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
          View GitHub Profile
        </a>
      </div>

      {/* Filters and Search */}
      <div className="mb-10 card p-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* Category filters */}
          <div className="flex flex-wrap gap-2 justify-center md:justify-start">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setFilter(category.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-300 ${
                  filter === category.id
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Search input */}
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 text-white"
            />
            <svg
              className="absolute right-3 top-2.5 w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Projects grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.length > 0 ? (
          filteredProjects.map((project, index) => (
            <ProjectCard
              key={index}
              project={project}
              delay={index * 100}
              isLoaded={isLoaded}
              index={index}
              onClick={setSelectedProject}
            />
          ))
        ) : (
          <div className="col-span-full text-center py-10">
            <p className="text-gray-400 text-lg">No projects match your search criteria. Try adjusting your filters.</p>
          </div>
        )}
      </div>

      {/* Project Details Popup */}
      {selectedProject && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 animate-fadeIn">
          <div
            className="relative bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-white z-10 bg-gray-800 rounded-full p-2"
              onClick={() => setSelectedProject(null)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Project image */}
            <div className="relative h-64 md:h-80">
              <img
                src={selectedProject.image}
                alt={selectedProject.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  // Try project-specific SVG placeholder if JPG is not available
                  const projectName = selectedProject.title.toLowerCase().replace(/\s+/g, '-');
                  e.target.src = `/images/projects/${projectName}.svg`;
                  // If project-specific SVG fails, try category placeholder
                  e.target.onerror = () => {
                    e.target.onerror = null;
                    e.target.src = `/images/projects/placeholder-${selectedProject.category}.svg`;
                    // If category SVG also fails, use a data URI as final fallback
                    e.target.onerror = () => {
                      e.target.onerror = null;
                      e.target.src = "data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='400' viewBox='0 0 800 400'%3E%3Crect fill='%23343a40' width='800' height='400'/%3E%3Ctext fill='rgba(255,255,255,0.7)' font-family='Arial' font-size='36' text-anchor='middle' x='400' y='200'%3E" + selectedProject.title + "%3C/text%3E%3C/svg%3E";
                    };
                  };
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>

              {/* Category badge */}
              <div className="absolute top-4 left-4">
                <div className="px-3 py-1 rounded-md text-sm font-medium bg-indigo-600 text-white">
                  {selectedProject.category.charAt(0).toUpperCase() + selectedProject.category.slice(1)}
                </div>
              </div>
            </div>

            {/* Project content */}
            <div className="p-6">
              <h2 className="text-2xl font-bold text-white mb-4">{selectedProject.title}</h2>

              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-200 mb-2">Description</h3>
                <p className="text-gray-300 leading-relaxed">
                  {selectedProject.longDescription || selectedProject.description}
                </p>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-200 mb-2">Technologies</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedProject.technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gray-800 text-sm rounded-md text-gray-300 border border-gray-700"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Project links */}
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href={selectedProject.github}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-secondary flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                  View on GitHub
                </a>
                <a
                  href={selectedProject.demo}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-primary flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  View Live Demo
                </a>
              </div>
            </div>
          </div>

          {/* Background overlay click handler */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => setSelectedProject(null)}
          ></div>
        </div>
      )}
    </div>
  );
};

const ProjectCard = ({ project, delay, isLoaded, onClick }) => {
  // Get category icon
  const CategoryIcon = () => {
    switch(project.category) {
      case 'web':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
          </svg>
        );
      case 'mobile':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        );
      case 'ai':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case 'cloud':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
          </svg>
        );
      case 'software':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
          </svg>
        );
    }
  };

  return (
    <div
      className={`card card-hover h-full flex flex-col animate-fadeIn cursor-pointer ${
        isLoaded ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
      onClick={() => onClick(project)}
    >
      {/* Category badge */}
      <div className="absolute top-3 right-3 z-20">
        <div className="flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium bg-gray-800 text-indigo-400 border border-gray-700">
          <CategoryIcon />
          <span>{project.category.charAt(0).toUpperCase() + project.category.slice(1)}</span>
        </div>
      </div>

      {/* Image */}
      <div className="relative overflow-hidden h-48">
        <img
          src={project.image}
          alt={project.title}
          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          onError={(e) => {
            e.target.onerror = null;
            // Try project-specific SVG placeholder if JPG is not available
            const projectName = project.title.toLowerCase().replace(/\s+/g, '-');
            e.target.src = `/images/projects/${projectName}.svg`;
            // If project-specific SVG fails, try category placeholder
            e.target.onerror = () => {
              e.target.onerror = null;
              e.target.src = `/images/projects/placeholder-${project.category}.svg`;
              // If category SVG also fails, use a data URI as final fallback
              e.target.onerror = () => {
                e.target.onerror = null;
                e.target.src = "data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='400' viewBox='0 0 800 400'%3E%3Crect fill='%23343a40' width='800' height='400'/%3E%3Ctext fill='rgba(255,255,255,0.7)' font-family='Arial' font-size='36' text-anchor='middle' x='400' y='200'%3E" + project.title + "%3C/text%3E%3C/svg%3E";
              };
            };
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/90 via-gray-900/50 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="p-5 flex-grow">
        <h3 className="text-lg font-medium text-white mb-2">{project.title}</h3>

        <p className="text-gray-400 mb-4 line-clamp-3">
          {project.description}
        </p>

        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-300 mb-2">Technologies:</h4>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-800 text-xs rounded-md text-gray-300 border border-gray-700"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="p-5 pt-0 mt-auto">
        <div className="flex space-x-3">
          <a
            href={project.github}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 btn btn-secondary text-sm flex items-center justify-center"
            onClick={(e) => e.stopPropagation()} // Prevent card click when clicking on link
          >
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            GitHub
          </a>
          <a
            href={project.demo}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 btn btn-primary text-sm flex items-center justify-center"
            onClick={(e) => e.stopPropagation()} // Prevent card click when clicking on link
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            Demo
          </a>
        </div>
      </div>

      {/* View details indicator */}
      <div className="absolute bottom-3 left-3 text-xs text-gray-400 flex items-center">
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        View Details
      </div>
    </div>
  );
};

export default Projects;
