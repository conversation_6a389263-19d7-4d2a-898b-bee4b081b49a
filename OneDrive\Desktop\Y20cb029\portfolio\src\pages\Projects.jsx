import { useState, useEffect } from 'react';

const Projects = () => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState(null);

  // Close popup when escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        setSelectedProject(null);
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, []);

  // Prevent body scrolling when popup is open
  useEffect(() => {
    if (selectedProject) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [selectedProject]);

  // Featured projects data
  const projects = [
    {
      id: 1,
      title: "Malware Detection with ML",
      description: "Advanced malware detection system using machine learning and deep learning techniques to identify and classify malicious software with high accuracy.",
      technologies: ["Python", "TensorFlow", "PyTorch", "Scikit-learn", "Computer Vision"],
      image: "/images/projects/malware-detection.jpg",
      github: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      demo: "https://github.com/karravulachandra/Malware-detection-with-ML-and-deep-learning",
      category: "ai",
      featured: true,
      status: "Completed",
      year: "2024",
      longDescription: "A sophisticated malware detection system that leverages machine learning and deep learning techniques to identify and classify malicious software with high accuracy. The system analyzes both static features (file metadata, headers, strings) and dynamic features (behavior patterns, API calls, network activity) to detect known and zero-day malware threats. Multiple models are implemented and compared, including traditional machine learning algorithms and deep learning approaches.",
      highlights: [
        "95% accuracy in malware detection",
        "Real-time threat analysis",
        "Multiple ML model comparison",
        "Explainable AI components"
      ]
    },
    {
      id: 2,
      title: "MLOps Fundamentals",
      description: "Comprehensive MLOps project showcasing model versioning, automated testing, CI/CD pipelines, and deployment strategies for machine learning models.",
      technologies: ["Python", "TensorFlow", "Docker", "Kubernetes", "CI/CD"],
      image: "/images/projects/mlops.jpg",
      github: "https://github.com/karravulachandra/Fundamentals-of-MLOps",
      demo: "https://github.com/karravulachandra/Fundamentals-of-MLOps",
      category: "ai",
      featured: true,
      status: "Completed",
      year: "2024",
      longDescription: "A comprehensive demonstration of MLOps practices for the entire machine learning lifecycle. This project showcases the infrastructure and workflows needed to reliably deploy and maintain machine learning models in production. Includes automated data validation, model versioning, containerization, and monitoring.",
      highlights: [
        "End-to-end ML pipeline automation",
        "Model versioning and experiment tracking",
        "Containerized deployment with Docker",
        "Kubernetes orchestration"
      ]
    },
    {
      id: 3,
      title: "VCare Healthcare System",
      description: "Comprehensive healthcare management system for patient care, appointment scheduling, and medical record management with modern web technologies.",
      technologies: ["Java", "Spring Boot", "React", "MySQL", "REST APIs"],
      image: "/images/projects/vcare.jpg",
      github: "https://github.com/karravulachandra/vcare",
      demo: "https://github.com/karravulachandra/vcare",
      category: "web",
      featured: true,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive healthcare management system built to modernize medical practice operations. VCare provides an integrated solution for patient management, appointment scheduling, electronic medical records, billing, and reporting. Features role-based access control, audit logging, and automated appointment reminders.",
      highlights: [
        "Complete patient management system",
        "Secure medical records handling",
        "Automated appointment scheduling",
        "Role-based access control"
      ]
    },
    {
      id: 4,
      title: "IndApp - Service Provider Platform",
      description: "Mobile application connecting independent service providers with customers, featuring booking system, reviews, and secure payment processing.",
      technologies: ["React Native", "Firebase", "Node.js", "Express", "Payment APIs"],
      image: "/images/projects/indapp.jpg",
      github: "https://github.com/karravulachandra/Indapp",
      demo: "https://github.com/karravulachandra/Indapp",
      category: "mobile",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive mobile platform that bridges the gap between independent service providers and customers. Built with React Native for cross-platform compatibility, Firebase for real-time database and authentication, and Node.js/Express for the backend API.",
      highlights: [
        "Cross-platform mobile app",
        "Real-time booking system",
        "Secure payment integration",
        "Review and rating system"
      ]
    },
    {
      id: 5,
      title: "GCP Cloud Architecture",
      description: "Scalable cloud application demonstrating Google Cloud Platform best practices, containerization, and microservices architecture.",
      technologies: ["Google Cloud", "Docker", "Kubernetes", "Python", "Microservices"],
      image: "/images/projects/gcp-project.jpg",
      github: "https://github.com/karravulachandra/GCP_Project",
      demo: "https://github.com/karravulachandra/GCP_Project",
      category: "cloud",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive demonstration of cloud-native application development and deployment on Google Cloud Platform. This project showcases best practices for building scalable, resilient, and maintainable cloud applications.",
      highlights: [
        "Kubernetes orchestration",
        "Auto-scaling implementation",
        "CI/CD pipeline integration",
        "Monitoring and logging"
      ]
    },
    {
      id: 6,
      title: "Web Development Portfolio",
      description: "Collection of modern web applications showcasing frontend and backend technologies, responsive design, and best practices.",
      technologies: ["JavaScript", "React", "Node.js", "Express", "MongoDB"],
      image: "/images/projects/web-development.jpg",
      github: "https://github.com/karravulachandra/web-development",
      demo: "https://github.com/karravulachandra/web-development",
      category: "web",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "A comprehensive repository of web development projects demonstrating proficiency in both frontend and backend technologies. This collection includes responsive websites, single-page applications, RESTful APIs, and full-stack applications.",
      highlights: [
        "Responsive design patterns",
        "RESTful API development",
        "Modern JavaScript (ES6+)",
        "Performance optimization"
      ]
    },
    {
      id: 7,
      title: "Village Agency Platform",
      description: "Digital platform connecting rural communities with government services, resources, and development programs to bridge the urban-rural divide.",
      technologies: ["JavaScript", "Node.js", "MongoDB", "Express", "Progressive Web App"],
      image: "/images/projects/village-agency.jpg",
      github: "https://github.com/karravulachandra/VillageAgency.1.0.0-",
      demo: "https://github.com/karravulachandra/VillageAgency.1.0.0-",
      category: "web",
      featured: false,
      status: "Completed",
      year: "2023",
      longDescription: "An innovative platform designed to bridge the digital divide between rural communities and government services. Village Agency serves as a centralized hub where rural residents can access information about government schemes and apply for benefits.",
      highlights: [
        "Government services integration",
        "Multilingual support",
        "Offline capability",
        "User-friendly interface"
      ]
    },
  ];

  // Project categories for filtering
  const categories = [
    { id: 'all', name: 'All Projects', count: projects.length },
    { id: 'ai', name: 'AI & ML', count: projects.filter(p => p.category === 'ai').length },
    { id: 'web', name: 'Web Development', count: projects.filter(p => p.category === 'web').length },
    { id: 'mobile', name: 'Mobile Apps', count: projects.filter(p => p.category === 'mobile').length },
    { id: 'cloud', name: 'Cloud & DevOps', count: projects.filter(p => p.category === 'cloud').length }
  ];

  // Filter projects based on category and search term
  const filteredProjects = projects.filter(project => {
    const matchesCategory = filter === 'all' || project.category === filter;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Get featured projects
  const featuredProjects = projects.filter(project => project.featured);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-950 dark:to-neutral-900">
        <div className="max-width-content container-padding">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              My <span className="text-gradient bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Projects</span>
            </h1>
            <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
              A showcase of my technical expertise through real-world projects spanning AI/ML, web development,
              mobile applications, and cloud technologies.
            </p>
          </div>

          {/* Featured Projects */}
          {featuredProjects.length > 0 && (
            <div className="mb-16">
              <h2 className="text-2xl md:text-3xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-8 text-center">
                Featured Projects
              </h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                {featuredProjects.map((project) => (
                  <FeaturedProjectCard
                    key={project.id}
                    project={project}
                    onClick={() => setSelectedProject(project)}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* All Projects Section */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-full container-padding">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              All Projects
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              Explore my complete portfolio of projects across different technologies and domains.
            </p>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setFilter(category.id)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  filter === category.id
                    ? 'bg-primary-600 text-white shadow-medium'
                    : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-200 dark:hover:bg-neutral-700'
                }`}
              >
                {category.name}
                <span className="ml-2 text-sm opacity-75">({category.count})</span>
              </button>
            ))}
          </div>

          {/* Search */}
          <div className="max-w-md mx-auto mb-12">
            <div className="relative">
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-12"
              />
              <svg className="w-5 h-5 text-neutral-400 absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onClick={() => setSelectedProject(project)}
              />
            ))}
          </div>

          {/* No Results */}
          {filteredProjects.length === 0 && (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                No projects found
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400">
                Try adjusting your search terms or filters.
              </p>
            </div>
          )}

          {/* GitHub Link */}
          <div className="text-center mt-16">
            <a
              href="https://github.com/karravulachandra"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-outline px-8 py-4 text-lg inline-flex items-center"
            >
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.30.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              View All on GitHub
            </a>
          </div>
        </div>
      </section>

      {/* Project Detail Modal */}
      {selectedProject && (
        <ProjectModal
          project={selectedProject}
          onClose={() => setSelectedProject(null)}
        />
      )}
    </div>
  );
};

// Featured Project Card Component
const FeaturedProjectCard = ({ project, onClick }) => (
  <div
    className="card-interactive group cursor-pointer overflow-hidden"
    onClick={onClick}
  >
    <div className="aspect-video bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/20 dark:to-accent-900/20 rounded-t-xl flex items-center justify-center mb-6">
      <div className="text-4xl">{getCategoryIcon(project.category)}</div>
    </div>

    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 text-sm font-medium rounded-full">
          {project.year}
        </span>
        <span className="px-3 py-1 bg-success-100 dark:bg-success-900/50 text-success-700 dark:text-success-300 text-sm font-medium rounded-full">
          {project.status}
        </span>
      </div>

      <h3 className="text-xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
        {project.title}
      </h3>

      <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-3">
        {project.description}
      </p>

      <div className="flex flex-wrap gap-2 mb-6">
        {project.technologies.slice(0, 3).map((tech, index) => (
          <span
            key={index}
            className="px-2 py-1 bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md"
          >
            {tech}
          </span>
        ))}
        {project.technologies.length > 3 && (
          <span className="px-2 py-1 bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md">
            +{project.technologies.length - 3} more
          </span>
        )}
      </div>

      <div className="flex items-center justify-between">
        <div className="flex space-x-3">
          <a
            href={project.github}
            target="_blank"
            rel="noopener noreferrer"
            className="text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
          </a>
          <a
            href={project.demo}
            target="_blank"
            rel="noopener noreferrer"
            className="text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
        </div>

        <div className="text-primary-600 dark:text-primary-400 font-medium text-sm group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-200">
          View Details →
        </div>
      </div>
    </div>
  </div>
);

// Regular Project Card Component
const ProjectCard = ({ project, onClick }) => (
  <div
    className="card-interactive group cursor-pointer"
    onClick={onClick}
  >
    <div className="aspect-video bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-900 rounded-t-xl flex items-center justify-center mb-4">
      <div className="text-3xl">{getCategoryIcon(project.category)}</div>
    </div>

    <div className="p-6">
      <div className="flex items-center justify-between mb-3">
        <span className="px-2 py-1 bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400 text-xs font-medium rounded-md">
          {project.year}
        </span>
        <span className="px-2 py-1 bg-success-100 dark:bg-success-900/50 text-success-600 dark:text-success-400 text-xs font-medium rounded-md">
          {project.status}
        </span>
      </div>

      <h3 className="text-lg font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
        {project.title}
      </h3>

      <p className="text-neutral-600 dark:text-neutral-400 text-sm mb-4 line-clamp-2">
        {project.description}
      </p>

      <div className="flex flex-wrap gap-1 mb-4">
        {project.technologies.slice(0, 2).map((tech, index) => (
          <span
            key={index}
            className="px-2 py-1 bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400 text-xs rounded-md"
          >
            {tech}
          </span>
        ))}
        {project.technologies.length > 2 && (
          <span className="px-2 py-1 bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400 text-xs rounded-md">
            +{project.technologies.length - 2}
          </span>
        )}
      </div>

      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <a
            href={project.github}
            target="_blank"
            rel="noopener noreferrer"
            className="text-neutral-500 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
          </a>
          <a
            href={project.demo}
            target="_blank"
            rel="noopener noreferrer"
            className="text-neutral-500 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
        </div>

        <div className="text-primary-600 dark:text-primary-400 font-medium text-xs group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-200">
          Details →
        </div>
      </div>
    </div>
  </div>
);

// Project Modal Component
const ProjectModal = ({ project, onClose }) => (
  <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 animate-fade-in">
    <div className="relative bg-white dark:bg-neutral-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
      {/* Close button */}
      <button
        className="absolute top-4 right-4 z-10 w-10 h-10 bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 transition-colors duration-200"
        onClick={onClose}
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>

      {/* Header */}
      <div className="p-8 border-b border-neutral-200 dark:border-neutral-800">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h2 className="text-3xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-2">
              {project.title}
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              {project.description}
            </p>
          </div>
          <div className="flex flex-col items-end space-y-2">
            <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 text-sm font-medium rounded-full">
              {project.year}
            </span>
            <span className="px-3 py-1 bg-success-100 dark:bg-success-900/50 text-success-700 dark:text-success-300 text-sm font-medium rounded-full">
              {project.status}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-8">
        {/* Description */}
        <div className="mb-8">
          <h3 className="text-xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
            Project Details
          </h3>
          <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
            {project.longDescription || project.description}
          </p>
        </div>

        {/* Highlights */}
        {project.highlights && (
          <div className="mb-8">
            <h3 className="text-xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
              Key Highlights
            </h3>
            <ul className="space-y-2">
              {project.highlights.map((highlight, index) => (
                <li key={index} className="flex items-start">
                  <svg className="w-5 h-5 text-success-600 dark:text-success-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-neutral-600 dark:text-neutral-400">{highlight}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Technologies */}
        <div className="mb-8">
          <h3 className="text-xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
            Technologies Used
          </h3>
          <div className="flex flex-wrap gap-3">
            {project.technologies.map((tech, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 rounded-lg font-medium"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <a
            href={project.github}
            target="_blank"
            rel="noopener noreferrer"
            className="btn-outline flex items-center justify-center px-6 py-3"
          >
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.30.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            View on GitHub
          </a>
          <a
            href={project.demo}
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary flex items-center justify-center px-6 py-3"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            View Live Demo
          </a>
        </div>
      </div>
    </div>

    {/* Background overlay */}
    <div
      className="absolute inset-0 -z-10"
      onClick={onClose}
    />
  </div>
);

// Helper function to get category icons
const getCategoryIcon = (category) => {
  const iconMap = {
    web: '🌐',
    mobile: '📱',
    ai: '🤖',
    cloud: '☁️',
    software: '💻'
  };
  return iconMap[category] || '⚡';
};

export default Projects;
