const About = () => {
  return (
    <div className="py-10">
      <h1 className="text-4xl font-bold mb-8 text-center">
        <span className="text-gradient-alt animate-neon">About</span> Me
      </h1>

      <div className="glass-card-dark p-6 rounded-lg shadow-lg mb-10 hover-lift">
        <h2 className="text-2xl font-semibold mb-4 text-blue-300">Who I Am</h2>
        <p className="text-gray-300 mb-4 leading-relaxed">
          Hello! I'm <PERSON><PERSON><PERSON><PERSON>, a passionate software developer and AI enthusiast with a B.Tech degree in Computer Science and Business Systems from Jawaharlal Nehru Technological University, Hyderabad.
          I specialize in building modern web applications and exploring the exciting field of artificial intelligence and machine learning.
        </p>
        <p className="text-gray-300 leading-relaxed">
          Currently working as an AI Intern at CAP Corporate AI Solutions LLP, I'm gaining hands-on experience in developing machine learning models and AI-powered applications.
          My journey in technology began during my undergraduate studies, where I developed a strong foundation in computer science principles,
          algorithms, and software development methodologies. Since then, I've been continuously expanding my skills and knowledge in both web development and AI technologies.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="glass-card-dark p-6 rounded-lg shadow-lg hover-lift">
          <h2 className="text-2xl font-semibold mb-4 text-gradient">Technical Focus</h2>
          <ul className="list-disc list-inside text-gray-300 space-y-2">
            <li>Full-stack web development with React, Node.js, and modern frameworks</li>
            <li>Machine learning and AI model development using TensorFlow and PyTorch</li>
            <li>Data analysis and visualization for extracting meaningful insights</li>
            <li>Cloud-based application deployment and DevOps practices</li>
            <li>Responsive and accessible user interface design</li>
          </ul>
        </div>

        <div className="glass-card-dark p-6 rounded-lg shadow-lg hover-lift">
          <h2 className="text-2xl font-semibold mb-4 text-gradient">My Approach</h2>
          <ul className="list-disc list-inside text-gray-300 space-y-2">
            <li>Focus on clean, maintainable, and well-documented code</li>
            <li>User-centered design thinking for intuitive experiences</li>
            <li>Continuous learning and staying updated with latest technologies</li>
            <li>Collaborative problem-solving and effective communication</li>
            <li>Attention to detail and commitment to quality</li>
            <li>Agile methodologies for efficient project management</li>
          </ul>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
        <div className="glass-card-dark p-6 rounded-lg shadow-lg hover-lift">
          <h2 className="text-2xl font-semibold mb-4 text-gradient">Personal Interests</h2>
          <ul className="list-disc list-inside text-gray-300 space-y-2">
            <li>Exploring emerging technologies and industry trends</li>
            <li>Contributing to open-source projects</li>
            <li>Technical writing and documentation</li>
            <li>Mentoring and knowledge sharing</li>
            <li>Participating in hackathons and coding competitions</li>
            <li>Reading tech blogs and research papers</li>
          </ul>
        </div>

        <div className="glass-card-dark p-6 rounded-lg shadow-lg hover-lift">
          <h2 className="text-2xl font-semibold mb-4 text-gradient">Professional Goals</h2>
          <ul className="list-disc list-inside text-gray-300 space-y-2">
            <li>Becoming an expert in AI and machine learning applications</li>
            <li>Building innovative solutions that solve real-world problems</li>
            <li>Mastering cloud-native application development</li>
            <li>Contributing to cutting-edge research in AI</li>
            <li>Leading technical teams on challenging projects</li>
            <li>Continuous professional development and certification</li>
          </ul>
        </div>
      </div>

      <div className="mt-10 glass-card-dark p-6 rounded-lg shadow-lg hover-lift">
        <h2 className="text-2xl font-semibold mb-4 text-gradient">Coding Profiles</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <a
            href="https://www.hackerrank.com/profile/karravulachandr1"
            target="_blank"
            rel="noopener noreferrer"
            className="card p-4 hover:bg-gray-800/50 transition-colors duration-300 flex flex-col items-center"
          >
            <div className="text-xl font-bold text-indigo-400 mb-2">HackerRank</div>
            <div className="text-gray-300 text-sm text-center">View my problem-solving skills and coding challenges</div>
          </a>

          <a
            href="https://www.codechef.com/users/chandu_332"
            target="_blank"
            rel="noopener noreferrer"
            className="card p-4 hover:bg-gray-800/50 transition-colors duration-300 flex flex-col items-center"
          >
            <div className="text-xl font-bold text-indigo-400 mb-2">CodeChef</div>
            <div className="text-gray-300 text-sm text-center">Check out my competitive programming solutions</div>
          </a>

          <a
            href="https://www.codingninjas.com/studio/profile/CHANDRAK"
            target="_blank"
            rel="noopener noreferrer"
            className="card p-4 hover:bg-gray-800/50 transition-colors duration-300 flex flex-col items-center"
          >
            <div className="text-xl font-bold text-indigo-400 mb-2">Coding Ninjas</div>
            <div className="text-gray-300 text-sm text-center">Explore my coding journey and achievements</div>
          </a>
        </div>
      </div>

      <div className="mt-8 glass-card-dark p-6 rounded-lg shadow-lg hover-lift">
        <h2 className="text-2xl font-semibold mb-4 text-gradient">My Vision</h2>
        <p className="text-gray-300 leading-relaxed">
          I aim to leverage technology, particularly AI and machine learning, to create meaningful solutions that positively impact users and businesses.
          I'm particularly interested in developing applications that are not only technically sound but also
          accessible, inclusive, and provide exceptional user experiences.
        </p>
        <p className="text-gray-300 mt-4 leading-relaxed">
          I believe in the power of technology to transform industries and improve lives. My goal is to be at the forefront of this transformation,
          contributing my skills and knowledge to innovative projects that push the boundaries of what's possible.
        </p>
        <p className="text-gray-300 mt-4 leading-relaxed">
          I'm always open to new opportunities, collaborations, and challenges that allow me to grow professionally
          while making a meaningful impact through technology.
        </p>

        <div className="mt-6 flex justify-center">
          <a
            href="https://www.linkedin.com/in/karravulachandra/"
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-primary"
          >
            Connect on LinkedIn
          </a>
        </div>
      </div>
    </div>
  );
};

export default About;
