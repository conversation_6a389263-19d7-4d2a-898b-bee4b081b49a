const About = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-950 dark:to-neutral-900">
        <div className="max-width-content container-padding">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              About <span className="text-gradient bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Me</span>
            </h1>
            <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
              Passionate software developer and AI enthusiast dedicated to creating innovative solutions
              that bridge technology and human needs.
            </p>
          </div>

          {/* Main Introduction */}
          <div className="card p-8 md:p-12 mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center">
              <div className="lg:col-span-2">
                <h2 className="text-2xl md:text-3xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-6">
                  Hello! I'm Karra Chaitanya
                </h2>
                <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-6 leading-relaxed">
                  A passionate software developer and AI enthusiast with a B.Tech degree in Computer Science and Business Systems
                  from Jawaharlal Nehru Technological University, Hyderabad. I specialize in building modern web applications
                  and exploring the exciting field of artificial intelligence and machine learning.
                </p>
                <p className="text-lg text-neutral-600 dark:text-neutral-400 leading-relaxed">
                  Currently working as an AI Intern at CAP Corporate AI Solutions LLP, I'm gaining hands-on experience in
                  developing machine learning models and AI-powered applications. My journey in technology began during my
                  undergraduate studies, where I developed a strong foundation in computer science principles.
                </p>
              </div>
              <div className="flex justify-center">
                <div className="w-64 h-64 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center">
                  <div className="w-56 h-56 bg-white dark:bg-neutral-900 rounded-xl flex items-center justify-center">
                    <svg className="w-32 h-32 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technical Focus & Approach */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-full container-padding">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              My Expertise
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              Combining technical skills with a user-centered approach to deliver exceptional results.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="card-interactive p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  Technical Focus
                </h3>
              </div>
              <div className="space-y-4">
                <SkillItem
                  title="Full-Stack Development"
                  description="React, Node.js, and modern frameworks for scalable applications"
                />
                <SkillItem
                  title="AI & Machine Learning"
                  description="TensorFlow and PyTorch for intelligent model development"
                />
                <SkillItem
                  title="Data Analysis"
                  description="Visualization and insights extraction from complex datasets"
                />
                <SkillItem
                  title="Cloud & DevOps"
                  description="Deployment strategies and cloud-native application development"
                />
                <SkillItem
                  title="UI/UX Design"
                  description="Responsive and accessible user interface design principles"
                />
              </div>
            </div>

            <div className="card-interactive p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-success-500 to-primary-500 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  My Approach
                </h3>
              </div>
              <div className="space-y-4">
                <SkillItem
                  title="Clean Code Philosophy"
                  description="Maintainable, well-documented, and scalable code practices"
                />
                <SkillItem
                  title="User-Centered Design"
                  description="Design thinking focused on intuitive user experiences"
                />
                <SkillItem
                  title="Continuous Learning"
                  description="Staying updated with latest technologies and best practices"
                />
                <SkillItem
                  title="Collaborative Problem-Solving"
                  description="Effective communication and teamwork for project success"
                />
                <SkillItem
                  title="Quality Commitment"
                  description="Attention to detail and commitment to excellence"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interests & Goals */}
      <section className="section-padding bg-neutral-50 dark:bg-neutral-900">
        <div className="max-width-full container-padding">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="card p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-warning-500 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  Personal Interests
                </h3>
              </div>
              <div className="space-y-4">
                <InterestItem
                  title="Emerging Technologies"
                  description="Exploring industry trends and cutting-edge innovations"
                />
                <InterestItem
                  title="Open Source Contribution"
                  description="Contributing to community projects and collaborative development"
                />
                <InterestItem
                  title="Technical Writing"
                  description="Documentation and knowledge sharing through writing"
                />
                <InterestItem
                  title="Mentoring"
                  description="Guiding others and sharing knowledge with the community"
                />
                <InterestItem
                  title="Competitive Programming"
                  description="Participating in hackathons and coding competitions"
                />
                <InterestItem
                  title="Research & Learning"
                  description="Reading tech blogs and staying updated with research papers"
                />
              </div>
            </div>

            <div className="card p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-success-500 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  Professional Goals
                </h3>
              </div>
              <div className="space-y-4">
                <InterestItem
                  title="AI Expertise"
                  description="Becoming an expert in AI and machine learning applications"
                />
                <InterestItem
                  title="Innovative Solutions"
                  description="Building solutions that solve real-world problems effectively"
                />
                <InterestItem
                  title="Cloud Mastery"
                  description="Mastering cloud-native application development and deployment"
                />
                <InterestItem
                  title="Research Contribution"
                  description="Contributing to cutting-edge research in artificial intelligence"
                />
                <InterestItem
                  title="Technical Leadership"
                  description="Leading technical teams on challenging and impactful projects"
                />
                <InterestItem
                  title="Continuous Growth"
                  description="Ongoing professional development and industry certifications"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Coding Profiles */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-content container-padding">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Coding Profiles
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              Explore my problem-solving skills and competitive programming journey.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <CodingProfileCard
              name="HackerRank"
              url="https://www.hackerrank.com/profile/karravulachandr1"
              description="Problem-solving skills and coding challenges"
              icon="hackerrank"
            />
            <CodingProfileCard
              name="CodeChef"
              url="https://www.codechef.com/users/chandu_332"
              description="Competitive programming solutions and contests"
              icon="codechef"
            />
            <CodingProfileCard
              name="Coding Ninjas"
              url="https://www.codingninjas.com/studio/profile/CHANDRAK"
              description="Coding journey and technical achievements"
              icon="codingninjas"
            />
          </div>
        </div>
      </section>

      {/* Vision & CTA */}
      <section className="section-padding bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/20 dark:to-accent-950/20">
        <div className="max-width-content container-padding">
          <div className="card p-8 md:p-12 text-center">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-8">
              My Vision
            </h2>
            <div className="space-y-6 text-lg text-neutral-600 dark:text-neutral-400 leading-relaxed max-w-4xl mx-auto">
              <p>
                I aim to leverage technology, particularly AI and machine learning, to create meaningful solutions that
                positively impact users and businesses. I'm particularly interested in developing applications that are
                not only technically sound but also accessible, inclusive, and provide exceptional user experiences.
              </p>
              <p>
                I believe in the power of technology to transform industries and improve lives. My goal is to be at the
                forefront of this transformation, contributing my skills and knowledge to innovative projects that push
                the boundaries of what's possible.
              </p>
              <p>
                I'm always open to new opportunities, collaborations, and challenges that allow me to grow professionally
                while making a meaningful impact through technology.
              </p>
            </div>

            <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://www.linkedin.com/in/karravulachandra/"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary px-8 py-4 text-lg"
              >
                Connect on LinkedIn
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
              <a
                href="/contact"
                className="btn-outline px-8 py-4 text-lg"
              >
                Get In Touch
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

// Helper Components
const SkillItem = ({ title, description }) => (
  <div className="flex items-start space-x-3">
    <div className="w-2 h-2 bg-primary-500 rounded-full mt-3 flex-shrink-0"></div>
    <div>
      <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-1">{title}</h4>
      <p className="text-neutral-600 dark:text-neutral-400 text-sm">{description}</p>
    </div>
  </div>
);

const InterestItem = ({ title, description }) => (
  <div className="flex items-start space-x-3">
    <div className="w-2 h-2 bg-accent-500 rounded-full mt-3 flex-shrink-0"></div>
    <div>
      <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-1">{title}</h4>
      <p className="text-neutral-600 dark:text-neutral-400 text-sm">{description}</p>
    </div>
  </div>
);

const CodingProfileCard = ({ name, url, description, icon }) => (
  <a
    href={url}
    target="_blank"
    rel="noopener noreferrer"
    className="card-interactive p-6 text-center group"
  >
    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
      {icon === 'hackerrank' && (
        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 0c6.627 0 12 5.373 12 12s-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0zm2.5 10.5L16 9v6l-1.5-1.5-1 1L12 13l-1.5 1.5-1-1L8 15V9l1.5 1.5 1-1L12 11l1.5-1.5 1 1z"/>
        </svg>
      )}
      {icon === 'codechef' && (
        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M11.5 0c6.347 0 11.5 5.153 11.5 11.5S17.847 23 11.5 23 0 17.847 0 11.5 5.153 0 11.5 0zm0 2C6.253 2 2 6.253 2 11.5S6.253 21 11.5 21 21 16.747 21 11.5 16.747 2 11.5 2z"/>
        </svg>
      )}
      {icon === 'codingninjas' && (
        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      )}
    </div>
    <h3 className="text-xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
      {name}
    </h3>
    <p className="text-neutral-600 dark:text-neutral-400 text-sm mb-4">
      {description}
    </p>
    <div className="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-200">
      View Profile
      <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
      </svg>
    </div>
  </a>
);

export default About;
