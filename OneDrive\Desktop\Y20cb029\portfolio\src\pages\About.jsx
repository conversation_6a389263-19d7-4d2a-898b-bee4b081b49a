const About = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-950 dark:to-neutral-900">
        <div className="max-width-content container-padding">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              About <span className="text-gradient bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Me</span>
            </h1>
            <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
              Passionate software developer and AI enthusiast dedicated to creating innovative solutions
              that bridge technology and human needs.
            </p>
          </div>

          {/* Main Introduction */}
          <div className="card p-8 md:p-12 mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center">
              <div className="lg:col-span-2">
                <h2 className="text-2xl md:text-3xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-6">
                  Hello! I'm Karra Chaitanya
                </h2>
                <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-6 leading-relaxed">
                  A passionate software developer and AI enthusiast with a B.Tech degree in Computer Science and Business Systems
                  from Jawaharlal Nehru Technological University, Hyderabad. I specialize in building modern web applications
                  and exploring the exciting field of artificial intelligence and machine learning.
                </p>
                <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-6 leading-relaxed">
                  Currently working as an AI Intern at CAP Corporate AI Solutions LLP, I'm gaining hands-on experience in
                  developing machine learning models and AI-powered applications. My journey in technology began during my
                  undergraduate studies, where I developed a strong foundation in computer science principles.
                </p>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                  <div className="text-center p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">4+</div>
                    <div className="text-sm text-neutral-600 dark:text-neutral-400">Years Learning</div>
                  </div>
                  <div className="text-center p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">15+</div>
                    <div className="text-sm text-neutral-600 dark:text-neutral-400">Projects</div>
                  </div>
                  <div className="text-center p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">10+</div>
                    <div className="text-sm text-neutral-600 dark:text-neutral-400">Technologies</div>
                  </div>
                  <div className="text-center p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                    <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">∞</div>
                    <div className="text-sm text-neutral-600 dark:text-neutral-400">Curiosity</div>
                  </div>
                </div>
              </div>
              <div className="flex justify-center">
                <div className="relative">
                  <div className="w-64 h-64 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl p-2 transform hover:scale-105 transition-transform duration-300">
                    <div className="w-full h-full bg-white dark:bg-neutral-900 rounded-xl overflow-hidden">
                      <img
                        src="/images/profile.jpg"
                        alt="Karra Chaitanya - Software Developer"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to placeholder if image doesn't exist
                          e.target.style.display = 'none';
                          e.target.nextElementSibling.style.display = 'flex';
                        }}
                      />
                      {/* Fallback placeholder */}
                      <div className="w-full h-full bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/20 dark:to-accent-900/20 flex items-center justify-center" style={{display: 'none'}}>
                        <svg className="w-32 h-32 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  {/* Floating elements */}
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-accent-500 rounded-full animate-bounce"></div>
                  <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-primary-500 rounded-full animate-pulse"></div>

                  {/* Professional badge */}
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                    <div className="bg-white dark:bg-neutral-900 px-4 py-2 rounded-full shadow-lg border border-neutral-200 dark:border-neutral-700">
                      <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">Software Developer</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Education & Experience Timeline */}
      <section className="section-padding bg-neutral-50 dark:bg-neutral-900">
        <div className="max-width-content container-padding">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              My Journey
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              A timeline of my educational background and professional experience.
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary-500 to-accent-500 rounded-full"></div>

            <div className="space-y-12">
              <TimelineItem
                year="2024"
                title="AI Intern"
                subtitle="CAP Corporate AI Solutions LLP"
                description="Developing machine learning models and AI-powered applications. Gaining hands-on experience in real-world AI implementations and contributing to innovative projects."
                type="experience"
                side="left"
              />

              <TimelineItem
                year="2021-2025"
                title="B.Tech in Computer Science and Business Systems"
                subtitle="Jawaharlal Nehru Technological University, Hyderabad"
                description="Comprehensive study of computer science fundamentals, business systems, and emerging technologies. Strong foundation in programming, algorithms, and software development."
                type="education"
                side="right"
              />

              <TimelineItem
                year="2019-2021"
                title="Intermediate Education"
                subtitle="Sri Chaitanya Junior College"
                description="Focused on Mathematics, Physics, and Chemistry with strong analytical and problem-solving skills development."
                type="education"
                side="left"
              />

              <TimelineItem
                year="2019"
                title="Secondary Education"
                subtitle="Bhashyam Public School"
                description="Completed secondary education with excellent academic performance and developed interest in technology and programming."
                type="education"
                side="right"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Technical Focus & Approach */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-full container-padding">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              My Expertise
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              Combining technical skills with a user-centered approach to deliver exceptional results.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="card-interactive p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  Technical Focus
                </h3>
              </div>
              <div className="space-y-4">
                <SkillItem
                  title="Full-Stack Development"
                  description="React, Node.js, and modern frameworks for scalable applications"
                />
                <SkillItem
                  title="AI & Machine Learning"
                  description="TensorFlow and PyTorch for intelligent model development"
                />
                <SkillItem
                  title="Data Analysis"
                  description="Visualization and insights extraction from complex datasets"
                />
                <SkillItem
                  title="Cloud & DevOps"
                  description="Deployment strategies and cloud-native application development"
                />
                <SkillItem
                  title="UI/UX Design"
                  description="Responsive and accessible user interface design principles"
                />
              </div>
            </div>

            <div className="card-interactive p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-success-500 to-primary-500 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  My Approach
                </h3>
              </div>
              <div className="space-y-4">
                <SkillItem
                  title="Clean Code Philosophy"
                  description="Maintainable, well-documented, and scalable code practices"
                />
                <SkillItem
                  title="User-Centered Design"
                  description="Design thinking focused on intuitive user experiences"
                />
                <SkillItem
                  title="Continuous Learning"
                  description="Staying updated with latest technologies and best practices"
                />
                <SkillItem
                  title="Collaborative Problem-Solving"
                  description="Effective communication and teamwork for project success"
                />
                <SkillItem
                  title="Quality Commitment"
                  description="Attention to detail and commitment to excellence"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Photo Gallery Section */}
      <section className="section-padding bg-neutral-50 dark:bg-neutral-900">
        <div className="max-width-content container-padding">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Gallery
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              A glimpse into my professional journey, projects, and experiences.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <PhotoCard
              src="/images/gallery/workspace.jpg"
              alt="My development workspace setup"
              title="Development Setup"
              description="My coding environment and workspace"
            />
            <PhotoCard
              src="/images/gallery/project-demo.jpg"
              alt="Presenting a project demo"
              title="Project Presentation"
              description="Demonstrating a machine learning project"
            />
            <PhotoCard
              src="/images/gallery/team-collaboration.jpg"
              alt="Team collaboration session"
              title="Team Collaboration"
              description="Working with the development team"
            />
            <PhotoCard
              src="/images/gallery/conference.jpg"
              alt="At a tech conference"
              title="Tech Conference"
              description="Learning from industry experts"
            />
            <PhotoCard
              src="/images/gallery/coding-session.jpg"
              alt="Coding session"
              title="Coding Session"
              description="Deep focus development work"
            />
            <PhotoCard
              src="/images/gallery/achievement.jpg"
              alt="Project achievement celebration"
              title="Achievement"
              description="Celebrating project milestones"
            />
          </div>
        </div>
      </section>

      {/* Interests & Goals */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-full container-padding">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Interests & Aspirations
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              What drives me and where I'm heading in my professional journey.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="card-interactive p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-warning-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  Personal Interests
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InterestCard
                  icon="🚀"
                  title="Emerging Technologies"
                  description="Exploring AI, blockchain, and quantum computing"
                />
                <InterestCard
                  icon="🌐"
                  title="Open Source"
                  description="Contributing to community projects"
                />
                <InterestCard
                  icon="✍️"
                  title="Technical Writing"
                  description="Sharing knowledge through documentation"
                />
                <InterestCard
                  icon="🎯"
                  title="Mentoring"
                  description="Guiding aspiring developers"
                />
                <InterestCard
                  icon="🏆"
                  title="Competitive Programming"
                  description="Hackathons and coding challenges"
                />
                <InterestCard
                  icon="📚"
                  title="Continuous Learning"
                  description="Research papers and tech blogs"
                />
              </div>
            </div>

            <div className="card-interactive p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-success-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-heading font-semibold text-neutral-900 dark:text-neutral-100">
                  Professional Goals
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InterestCard
                  icon="🤖"
                  title="AI Expertise"
                  description="Mastering machine learning and deep learning"
                />
                <InterestCard
                  icon="💡"
                  title="Innovation"
                  description="Building solutions for real-world problems"
                />
                <InterestCard
                  icon="☁️"
                  title="Cloud Mastery"
                  description="Cloud-native development expertise"
                />
                <InterestCard
                  icon="🔬"
                  title="Research"
                  description="Contributing to AI research"
                />
                <InterestCard
                  icon="👥"
                  title="Leadership"
                  description="Leading technical teams"
                />
                <InterestCard
                  icon="📈"
                  title="Growth"
                  description="Continuous professional development"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Coding Profiles */}
      <section className="section-padding bg-white dark:bg-neutral-950">
        <div className="max-width-content container-padding">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Coding Profiles
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              Explore my problem-solving skills and competitive programming journey.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <CodingProfileCard
              name="HackerRank"
              url="https://www.hackerrank.com/profile/karravulachandr1"
              description="Problem-solving skills and coding challenges"
              icon="hackerrank"
            />
            <CodingProfileCard
              name="CodeChef"
              url="https://www.codechef.com/users/chandu_332"
              description="Competitive programming solutions and contests"
              icon="codechef"
            />
            <CodingProfileCard
              name="Coding Ninjas"
              url="https://www.codingninjas.com/studio/profile/CHANDRAK"
              description="Coding journey and technical achievements"
              icon="codingninjas"
            />
          </div>
        </div>
      </section>

      {/* Vision & CTA */}
      <section className="section-padding bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/20 dark:to-accent-950/20">
        <div className="max-width-content container-padding">
          <div className="card p-8 md:p-12 text-center">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-neutral-900 dark:text-neutral-100 mb-8">
              My Vision
            </h2>
            <div className="space-y-6 text-lg text-neutral-600 dark:text-neutral-400 leading-relaxed max-w-4xl mx-auto">
              <p>
                I aim to leverage technology, particularly AI and machine learning, to create meaningful solutions that
                positively impact users and businesses. I'm particularly interested in developing applications that are
                not only technically sound but also accessible, inclusive, and provide exceptional user experiences.
              </p>
              <p>
                I believe in the power of technology to transform industries and improve lives. My goal is to be at the
                forefront of this transformation, contributing my skills and knowledge to innovative projects that push
                the boundaries of what's possible.
              </p>
              <p>
                I'm always open to new opportunities, collaborations, and challenges that allow me to grow professionally
                while making a meaningful impact through technology.
              </p>
            </div>

            <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://www.linkedin.com/in/karravulachandra/"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary px-8 py-4 text-lg"
              >
                Connect on LinkedIn
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
              <a
                href="/contact"
                className="btn-outline px-8 py-4 text-lg"
              >
                Get In Touch
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

// Helper Components
const SkillItem = ({ title, description }) => (
  <div className="flex items-start space-x-3">
    <div className="w-2 h-2 bg-primary-500 rounded-full mt-3 flex-shrink-0"></div>
    <div>
      <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-1">{title}</h4>
      <p className="text-neutral-600 dark:text-neutral-400 text-sm">{description}</p>
    </div>
  </div>
);

// Timeline Item Component
const TimelineItem = ({ year, title, subtitle, description, type, side }) => (
  <div className={`flex items-center ${side === 'left' ? 'flex-row-reverse' : ''}`}>
    <div className={`w-1/2 ${side === 'left' ? 'pl-8' : 'pr-8'}`}>
      <div className={`card p-6 ${side === 'left' ? 'text-right' : ''}`}>
        <div className="flex items-center mb-3">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
            type === 'experience'
              ? 'bg-gradient-to-br from-success-500 to-primary-500'
              : 'bg-gradient-to-br from-primary-500 to-accent-500'
          } ${side === 'left' ? 'order-2 ml-3 mr-0' : ''}`}>
            {type === 'experience' ? (
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6" />
              </svg>
            ) : (
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            )}
          </div>
          <span className="text-sm font-medium text-primary-600 dark:text-primary-400">{year}</span>
        </div>
        <h3 className="text-xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          {title}
        </h3>
        <h4 className="text-lg font-medium text-neutral-700 dark:text-neutral-300 mb-3">
          {subtitle}
        </h4>
        <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
          {description}
        </p>
      </div>
    </div>

    {/* Timeline dot */}
    <div className="w-4 h-4 bg-white dark:bg-neutral-900 border-4 border-primary-500 rounded-full z-10"></div>

    <div className="w-1/2"></div>
  </div>
);

// Interest Card Component
const InterestCard = ({ icon, title, description }) => (
  <div className="p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors duration-200 group">
    <div className="text-2xl mb-2 group-hover:scale-110 transition-transform duration-200">{icon}</div>
    <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-1">{title}</h4>
    <p className="text-neutral-600 dark:text-neutral-400 text-sm">{description}</p>
  </div>
);

// Photo Card Component
const PhotoCard = ({ src, alt, title, description }) => (
  <div className="card-interactive group overflow-hidden">
    <div className="aspect-square bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-900 overflow-hidden relative">
      <img
        src={src}
        alt={alt}
        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        onError={(e) => {
          // Fallback to placeholder if image doesn't exist
          e.target.style.display = 'none';
          e.target.nextElementSibling.style.display = 'flex';
        }}
      />
      {/* Fallback placeholder */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/20 dark:to-accent-900/20 flex items-center justify-center" style={{display: 'none'}}>
        <svg className="w-16 h-16 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      {/* Content overlay */}
      <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <h3 className="font-semibold mb-1">{title}</h3>
        <p className="text-sm opacity-90">{description}</p>
      </div>
    </div>
  </div>
);

const CodingProfileCard = ({ name, url, description, icon }) => (
  <a
    href={url}
    target="_blank"
    rel="noopener noreferrer"
    className="card-interactive p-6 text-center group"
  >
    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
      {icon === 'hackerrank' && (
        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 0c6.627 0 12 5.373 12 12s-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0zm2.5 10.5L16 9v6l-1.5-1.5-1 1L12 13l-1.5 1.5-1-1L8 15V9l1.5 1.5 1-1L12 11l1.5-1.5 1 1z"/>
        </svg>
      )}
      {icon === 'codechef' && (
        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M11.5 0c6.347 0 11.5 5.153 11.5 11.5S17.847 23 11.5 23 0 17.847 0 11.5 5.153 0 11.5 0zm0 2C6.253 2 2 6.253 2 11.5S6.253 21 11.5 21 21 16.747 21 11.5 16.747 2 11.5 2z"/>
        </svg>
      )}
      {icon === 'codingninjas' && (
        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      )}
    </div>
    <h3 className="text-xl font-heading font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
      {name}
    </h3>
    <p className="text-neutral-600 dark:text-neutral-400 text-sm mb-4">
      {description}
    </p>
    <div className="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-200">
      View Profile
      <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
      </svg>
    </div>
  </a>
);

export default About;
