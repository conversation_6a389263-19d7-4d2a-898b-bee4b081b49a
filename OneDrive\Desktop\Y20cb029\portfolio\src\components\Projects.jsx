import { useState, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { FaGithub, FaExternalLinkAlt, FaFolder, FaCode, FaLaptopCode, FaMobileAlt, FaDatabase, FaRobot, FaCogs } from 'react-icons/fa';

// Import custom components
import Card3D from './ui/Card3D';
import AnimatedText from './ui/AnimatedText';
import { useStaggeredAnimation } from '../hooks/useAnimations';

const Projects = () => {
  const [filter, setFilter] = useState('all');

  const featuredProjects = [
    {
      title: "AI-Powered Customer Support System",
      description: "A machine learning-based customer support system that automatically categorizes and routes customer inquiries, provides suggested responses, and learns from agent interactions to improve over time.",
      image: "/images/project1.jpg", // Replace with actual image path
      technologies: ["Python", "TensorFlow", "Natural Language Processing", "Flask", "React"],
      github: "https://github.com/karrachandrasekhar/ai-customer-support",
      live: "https://ai-customer-support-demo.herokuapp.com",
      category: "ai"
    },
    {
      title: "Smart Inventory Management System",
      description: "An intelligent inventory management solution that uses predictive analytics to forecast stock needs, automates reordering processes, and provides real-time inventory visibility across multiple locations.",
      image: "/images/project2.jpg", // Replace with actual image path
      technologies: ["React", "Node.js", "MongoDB", "Express", "Chart.js"],
      github: "https://github.com/karrachandrasekhar/smart-inventory",
      live: "https://smart-inventory-system.netlify.app",
      category: "web"
    },
    {
      title: "Cloud-Based Data Analytics Platform",
      description: "A scalable data analytics platform that processes and visualizes large datasets, supports custom queries, and generates automated reports with insights derived from business data.",
      image: "/images/project3.jpg", // Replace with actual image path
      technologies: ["AWS", "Python", "PostgreSQL", "D3.js", "React"],
      github: "https://github.com/karrachandrasekhar/cloud-analytics",
      live: "https://cloud-analytics-platform.vercel.app",
      category: "data"
    }
  ];

  const otherProjects = [
    {
      title: "Personal Portfolio Website",
      description: "A modern, responsive portfolio website built with React and Tailwind CSS to showcase my projects, skills, and professional experience.",
      technologies: ["React", "Tailwind CSS", "Framer Motion", "Vite"],
      github: "https://github.com/karrachandrasekhar/portfolio",
      live: "https://karrachandrasekhar.dev",
      category: "web"
    },
    {
      title: "Database Optimization Tool",
      description: "A utility for analyzing and optimizing database performance, identifying bottlenecks, and suggesting improvements for query efficiency.",
      technologies: ["Python", "SQL", "PostgreSQL", "SQLAlchemy"],
      github: "https://github.com/karrachandrasekhar/db-optimizer",
      category: "data"
    },
    {
      title: "E-Learning Platform",
      description: "A web application for online learning with features like course management, progress tracking, quizzes, and interactive content delivery.",
      technologies: ["React", "Node.js", "MongoDB", "Express", "JWT"],
      github: "https://github.com/karrachandrasekhar/e-learning-platform",
      category: "web"
    },
    {
      title: "Real-time Collaboration Tool",
      description: "A collaborative workspace application with real-time document editing, chat functionality, and project management features.",
      technologies: ["React", "Socket.io", "Node.js", "Redis"],
      github: "https://github.com/karrachandrasekhar/collab-tool",
      live: "https://realtime-collab.netlify.app",
      category: "web"
    },
    {
      title: "Sentiment Analysis API",
      description: "An API service that analyzes text data to determine sentiment, extracts key entities, and provides emotional tone assessment.",
      technologies: ["Python", "Flask", "NLTK", "spaCy", "Docker"],
      github: "https://github.com/karrachandrasekhar/sentiment-api",
      category: "ai"
    },
    {
      title: "Mobile Weather App",
      description: "A cross-platform mobile application providing detailed weather forecasts, alerts, and location-based weather information.",
      technologies: ["React Native", "Redux", "Weather API", "Geolocation"],
      github: "https://github.com/karrachandrasekhar/weather-mobile",
      category: "mobile"
    }
  ];

  const filteredOtherProjects = filter === 'all'
    ? otherProjects
    : otherProjects.filter(project => project.category === filter);

  // Refs for scroll animations
  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  // Parallax effect values
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const y3 = useTransform(scrollYProgress, [0, 1], [0, -150]);

  // Use custom staggered animation hook
  const { ref: projectsRef, isInView, containerVariants, itemVariants } = useStaggeredAnimation(0.05);

  // Enhanced categories with icons and colors
  const categories = [
    { value: 'all', label: 'All', icon: <FaCode />, color: 'secondary' },
    { value: 'web', label: 'Web', icon: <FaLaptopCode />, color: 'secondary' },
    { value: 'mobile', label: 'Mobile', icon: <FaMobileAlt />, color: 'accent' },
    { value: 'data', label: 'Data', icon: <FaDatabase />, color: 'tertiary' },
    { value: 'ai', label: 'AI & ML', icon: <FaRobot />, color: 'accent' },
    { value: 'software', label: 'Software', icon: <FaCogs />, color: 'tertiary' }
  ];

  return (
    <section id="projects" className="py-20 bg-primary relative overflow-hidden" ref={sectionRef}>
      {/* Background elements with parallax effect */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-20 right-10 w-64 h-64 rounded-full bg-secondary/5 blur-[100px]"
          style={{ y: y1 }}
        />
        <motion.div
          className="absolute bottom-40 left-20 w-80 h-80 rounded-full bg-accent/5 blur-[120px]"
          style={{ y: y2 }}
        />
        <motion.div
          className="absolute top-1/2 right-1/3 w-40 h-40 rounded-full bg-tertiary/5 blur-[80px]"
          style={{ y: y3 }}
        />

        {/* Grid pattern */}
        <div className="absolute inset-0 bg-grid-animated opacity-10"></div>
      </div>

      <div className="section-container relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center text-center mb-12"
        >
          <h2 className="section-title mb-4">
            <span className="text-secondary font-mono">05.</span> Projects
          </h2>
          <AnimatedText
            text="My Creative Portfolio"
            className="text-xl text-light/80 max-w-2xl"
            type="gradient"
          />
        </motion.div>

        {/* Featured Projects */}
        <div className="mt-10 space-y-32" ref={projectsRef}>
          {featuredProjects.map((project, index) => (
            <motion.div
              key={index}
              className={`md:grid md:grid-cols-12 gap-8 items-center ${
                index % 2 === 0 ? '' : 'md:flex-row-reverse'
              }`}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.7,
                delay: 0.1,
                type: "spring",
                stiffness: 50
              }}
            >
              {/* Project Image */}
              <div className={`md:col-span-7 relative ${
                index % 2 === 0 ? 'md:order-1' : 'md:order-2'
              }`}>
                <Card3D
                  className="overflow-hidden rounded-lg"
                  glowColor={index % 2 === 0 ? "rgba(10, 240, 255, 0.7)" : "rgba(138, 77, 255, 0.7)"}
                  intensity={8}
                >
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-tr from-secondary/30 to-accent/20 rounded-lg transition-all duration-300 group-hover:opacity-0 z-10"></div>
                    <div className="relative rounded-lg overflow-hidden">
                      {/* This will show a placeholder until you add real images */}
                      <div className="aspect-video bg-dark/80 w-full flex items-center justify-center relative">
                        {/* Animated code background */}
                        <div className="absolute inset-0 opacity-20 overflow-hidden">
                          <pre className="text-xs text-secondary/70 font-mono">
                            {Array(20).fill().map((_, i) => (
                              <motion.div
                                key={i}
                                initial={{ opacity: 0.3 }}
                                animate={{ opacity: [0.3, 0.7, 0.3] }}
                                transition={{
                                  duration: 3,
                                  delay: i * 0.1,
                                  repeat: Infinity
                                }}
                              >
                                {`const ${project.title.replace(/\s+/g, '')} = () => { return <Component data={${i}} /> }`}
                              </motion.div>
                            ))}
                          </pre>
                        </div>

                        <motion.div
                          className="relative z-10 bg-dark/80 p-4 rounded-lg backdrop-blur-sm border border-secondary/30"
                          whileHover={{ scale: 1.05 }}
                          transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                          <span className="text-secondary font-mono">Project Image Coming Soon</span>
                        </motion.div>
                      </div>
                      {/* Uncomment this when you have your project images
                      <img
                        src={project.image}
                        alt={project.title}
                        className="w-full h-full object-cover"
                      />
                      */}
                    </div>
                  </div>
                </Card3D>
              </div>

              {/* Project Info */}
              <div className={`md:col-span-5 ${
                index % 2 === 0 ? 'md:order-2 md:text-right' : 'md:order-1'
              }`}>
                <motion.p
                  className="text-secondary font-mono text-sm mb-1"
                  initial={{ opacity: 0, x: index % 2 === 0 ? 20 : -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  Featured Project
                </motion.p>

                <motion.h3
                  className="text-3xl font-bold mb-4"
                  initial={{ opacity: 0, x: index % 2 === 0 ? 20 : -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <span className="text-gradient">{project.title}</span>
                </motion.h3>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <Card3D
                    className="overflow-hidden rounded-lg"
                    glowColor={index % 2 === 0 ? "rgba(10, 240, 255, 0.3)" : "rgba(138, 77, 255, 0.3)"}
                    intensity={5}
                    borderGlow={false}
                  >
                    <div className="bg-dark/80 p-6 rounded-lg backdrop-blur-sm border border-light/10">
                      <p className="text-light/80">{project.description}</p>
                    </div>
                  </Card3D>
                </motion.div>

                <motion.ul
                  className={`flex flex-wrap gap-3 my-6 ${
                    index % 2 === 0 ? 'md:justify-end' : ''
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  {project.technologies.map((tech, techIndex) => (
                    <motion.li
                      key={techIndex}
                      className="px-3 py-1 bg-dark/50 rounded-full text-light/70 font-mono text-xs border border-light/10 hover:border-secondary/50 transition-all duration-300"
                      whileHover={{
                        y: -5,
                        scale: 1.05,
                        transition: { type: "spring", stiffness: 300, damping: 10 }
                      }}
                    >
                      {tech}
                    </motion.li>
                  ))}
                </motion.ul>

                <motion.div
                  className={`flex gap-4 ${
                    index % 2 === 0 ? 'md:justify-end' : ''
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <motion.a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-light hover:text-secondary transition-all duration-300 p-3 border border-light/20 hover:border-secondary rounded-full hover:shadow-neon"
                    whileHover={{ scale: 1.2, rotate: 10 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FaGithub size={20} />
                  </motion.a>
                  {project.live && (
                    <motion.a
                      href={project.live}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-light hover:text-secondary transition-all duration-300 p-3 border border-light/20 hover:border-secondary rounded-full hover:shadow-neon"
                      whileHover={{ scale: 1.2, rotate: -10 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FaExternalLinkAlt size={18} />
                    </motion.a>
                  )}
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Other Projects */}
        <motion.div
          className="mt-36"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{
            duration: 0.7,
            type: "spring",
            stiffness: 50
          }}
        >
          <div className="text-center mb-16">
            <AnimatedText
              text="Other Noteworthy Projects"
              className="text-3xl font-bold text-lightest mb-4"
              type="glow"
            />
            <p className="text-light/70 max-w-2xl mx-auto">
              A collection of other projects I've worked on that showcase different skills and technologies.
            </p>
          </div>

          {/* Filter Buttons */}
          <motion.div
            className="flex justify-center mb-12"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <div className="flex flex-wrap gap-3 justify-center">
              {categories.map((category, index) => (
                <motion.div
                  key={category.value}
                  variants={itemVariants}
                  whileHover={{
                    y: -5,
                    transition: { type: "spring", stiffness: 300, damping: 10 }
                  }}
                >
                  <Card3D
                    className="overflow-hidden rounded-full"
                    glowColor={`var(--color-${category.color})`}
                    intensity={filter === category.value ? 10 : 5}
                    borderGlow={filter === category.value}
                  >
                    <button
                      onClick={() => setFilter(category.value)}
                      className={`px-5 py-2 rounded-full text-sm font-mono transition-all duration-300 flex items-center gap-2 ${
                        filter === category.value
                          ? `bg-${category.color} text-primary`
                          : `bg-dark text-light hover:bg-${category.color}/20`
                      }`}
                    >
                      <span className="text-base">{category.icon}</span>
                      {category.label}
                    </button>
                  </Card3D>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Projects Grid */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
          >
            {filteredOtherProjects.map((project, index) => {
              // Find the category object for this project
              const projectCategory = categories.find(cat => cat.value === project.category) || categories[0];

              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{
                    y: -10,
                    transition: { type: "spring", stiffness: 300, damping: 10 }
                  }}
                >
                  <Card3D
                    className="h-full"
                    glowColor={`var(--color-${projectCategory.color})`}
                    intensity={5}
                  >
                    <div className="bg-dark/80 backdrop-blur-sm rounded-lg p-6 border border-light/5 h-full flex flex-col">
                      <div className="flex justify-between items-center mb-5">
                        <div className={`text-${projectCategory.color} text-3xl`}>
                          {projectCategory.icon}
                        </div>
                        <div className="flex gap-4">
                          <motion.a
                            href={project.github}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-light hover:text-secondary transition-all duration-300"
                            whileHover={{ scale: 1.2, rotate: 10 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <FaGithub size={18} />
                          </motion.a>
                          {project.live && (
                            <motion.a
                              href={project.live}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-light hover:text-secondary transition-all duration-300"
                              whileHover={{ scale: 1.2, rotate: -10 }}
                              whileTap={{ scale: 0.9 }}
                            >
                              <FaExternalLinkAlt size={16} />
                            </motion.a>
                          )}
                        </div>
                      </div>

                      <h4 className="text-xl font-bold text-lightest mb-3 hover:text-secondary transition-colors duration-300">
                        {project.title}
                      </h4>

                      <p className="text-light/70 mb-6 flex-grow">{project.description}</p>

                      <div className="flex flex-wrap gap-2 mt-auto pt-4 border-t border-light/10">
                        {project.technologies.map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className={`px-2 py-1 bg-${projectCategory.color}/10 rounded-md text-${projectCategory.color}/80 font-mono text-xs border border-${projectCategory.color}/20`}
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </Card3D>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
