import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiRefresh<PERSON>w } from 'react-icons/fi';

const EmptyState = ({
  title = "No results found",
  description = "Try adjusting your search terms or filters",
  icon: Icon = FiSearch,
  actionLabel = "Clear Filters",
  onAction,
  className = ""
}) => {
  const iconVariants = {
    initial: { scale: 0, rotate: -180 },
    animate: { 
      scale: 1, 
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15
      }
    }
  };

  const containerVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      className={`text-center py-20 ${className}`}
    >
      {/* Icon */}
      <motion.div
        variants={iconVariants}
        className="inline-flex items-center justify-center w-20 h-20 bg-dark rounded-full border border-light/20 mb-6"
      >
        <Icon className="w-8 h-8 text-light/50" />
      </motion.div>

      {/* Title */}
      <motion.h3
        variants={itemVariants}
        className="text-xl font-semibold text-light mb-2"
      >
        {title}
      </motion.h3>

      {/* Description */}
      <motion.p
        variants={itemVariants}
        className="text-light/60 mb-8 max-w-md mx-auto"
      >
        {description}
      </motion.p>

      {/* Action Button */}
      {onAction && (
        <motion.button
          variants={itemVariants}
          onClick={onAction}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="inline-flex items-center gap-2 px-6 py-3 bg-secondary text-primary rounded-xl font-medium hover:bg-secondary/90 transition-all duration-300 shadow-neon"
        >
          <FiRefreshCw className="w-4 h-4" />
          {actionLabel}
        </motion.button>
      )}

      {/* Decorative Elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1]
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 4, repeat: Infinity, ease: "easeInOut" }
          }}
          className="absolute top-1/4 left-1/4 w-2 h-2 bg-secondary/20 rounded-full"
        />
        <motion.div
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1]
          }}
          transition={{
            rotate: { duration: 15, repeat: Infinity, ease: "linear" },
            scale: { duration: 3, repeat: Infinity, ease: "easeInOut" }
          }}
          className="absolute bottom-1/4 right-1/4 w-3 h-3 bg-accent/20 rounded-full"
        />
      </div>
    </motion.div>
  );
};

export default EmptyState;
import { FiSearch, FiFilter, FiRefreshCw } from 'react-icons/fi';

const EmptyState = ({
  title = "No results found",
  description = "Try adjusting your search terms or filters",
  icon: Icon = FiSearch,
  actionLabel = "Clear Filters",
  onAction,
  className = ""
}) => {
  const iconVariants = {
    initial: { scale: 0, rotate: -180 },
    animate: { 
      scale: 1, 
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15
      }
    }
  };

  const containerVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      className={`text-center py-20 ${className}`}
    >
      {/* Icon */}
      <motion.div
        variants={iconVariants}
        className="inline-flex items-center justify-center w-20 h-20 bg-dark rounded-full border border-light/20 mb-6"
      >
        <Icon className="w-8 h-8 text-light/50" />
      </motion.div>

      {/* Title */}
      <motion.h3
        variants={itemVariants}
        className="text-xl font-semibold text-light mb-2"
      >
        {title}
      </motion.h3>

      {/* Description */}
      <motion.p
        variants={itemVariants}
        className="text-light/60 mb-8 max-w-md mx-auto"
      >
        {description}
      </motion.p>

      {/* Action Button */}
      {onAction && (
        <motion.button
          variants={itemVariants}
          onClick={onAction}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="inline-flex items-center gap-2 px-6 py-3 bg-secondary text-primary rounded-xl font-medium hover:bg-secondary/90 transition-all duration-300 shadow-neon"
        >
          <FiRefreshCw className="w-4 h-4" />
          {actionLabel}
        </motion.button>
      )}

      {/* Decorative Elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1]
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 4, repeat: Infinity, ease: "easeInOut" }
          }}
          className="absolute top-1/4 left-1/4 w-2 h-2 bg-secondary/20 rounded-full"
        />
        <motion.div
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1]
          }}
          transition={{
            rotate: { duration: 15, repeat: Infinity, ease: "linear" },
            scale: { duration: 3, repeat: Infinity, ease: "easeInOut" }
          }}
          className="absolute bottom-1/4 right-1/4 w-3 h-3 bg-accent/20 rounded-full"
        />
      </div>
    </motion.div>
  );
};
