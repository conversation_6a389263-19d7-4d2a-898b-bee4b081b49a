import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

const Home = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  // Animated text typing effect
  const [typedText, setTypedText] = useState('');
  const fullText = "Software Developer | B.Tech CSBS Graduate";
  const [typingIndex, setTypingIndex] = useState(0);

  useEffect(() => {
    // Set loaded state after a small delay for animations
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (typingIndex < fullText.length) {
      const timeout = setTimeout(() => {
        setTypedText(prevText => prevText + fullText[typingIndex]);
        setTypingIndex(prevIndex => prevIndex + 1);
      }, 50); // Adjust typing speed

      return () => clearTimeout(timeout);
    }
  }, [typingIndex, fullText]);

  // Animated background elements
  const particles = Array.from({ length: 50 }, (_, i) => ({
    id: i,
    size: Math.random() * 4 + 1,
    x: Math.random() * 100,
    y: Math.random() * 100,
    animationDuration: Math.random() * 20 + 10,
    delay: Math.random() * 5
  }));

  return (
    <div className="min-h-[calc(100vh-80px)] flex flex-col items-center justify-center text-center relative overflow-hidden">
      {/* Subtle background elements */}
      <div className="absolute inset-0 overflow-hidden opacity-5 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-indigo-700 via-transparent to-transparent opacity-20"></div>
      </div>

      {/* Subtle particles */}
      {particles.map(particle => (
        <div
          key={particle.id}
          className="absolute rounded-full bg-white opacity-10"
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            animation: `float ${particle.animationDuration}s infinite`,
            animationDelay: `${particle.delay}s`
          }}
        />
      ))}

      {/* Main content with animations */}
      <div className="relative z-10 px-4 w-full max-w-4xl">
        <div className={`transition-all duration-700 ${isLoaded ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
          {/* Name */}
          <div className="mb-6">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-2">
              Karravula Chandra
            </h1>
            <div className="h-1 w-24 bg-indigo-500 mx-auto"></div>
          </div>

          {/* Animated typing effect */}
          <div className="mb-8">
            <h2 className="text-xl md:text-2xl text-gray-300 font-medium relative inline-flex">
              <span>{typedText}</span>
              <span className="w-1 h-8 bg-indigo-400 ml-1 animate-pulse"></span>
            </h2>
          </div>

          {/* Description */}
          <div className="max-w-2xl mx-auto mb-12">
            <p className="text-gray-300 leading-relaxed">
              Welcome to my portfolio! I'm a passionate software developer with expertise in modern web technologies.
              Explore my projects, skills, and experience to learn more about my work.
            </p>
          </div>
        </div>

        {/* Feature cards */}
        <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 transition-all duration-700 delay-200 ${isLoaded ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
          <FeatureCard
            title="About Me"
            description="Learn more about my background and interests"
            link="/about"
            delay={0}
            icon="user"
          />
          <FeatureCard
            title="Skills"
            description="Discover my technical expertise and capabilities"
            link="/skills"
            delay={100}
            icon="code"
          />
          <FeatureCard
            title="Projects"
            description="Explore the projects I've worked on"
            link="/projects"
            delay={200}
            icon="projects"
          />
          <FeatureCard
            title="Contact"
            description="Get in touch with me for opportunities"
            link="/contact"
            delay={300}
            icon="contact"
          />
        </div>

        {/* Social links */}
        <div className={`flex justify-center space-x-6 transition-all duration-700 delay-300 ${isLoaded ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
          <SocialLink href="https://github.com/karrachandrasekhar" label="GitHub" icon="github" />
          <SocialLink href="https://www.linkedin.com/in/karravulachandra/" label="LinkedIn" icon="linkedin" />
          <SocialLink href="mailto:<EMAIL>" label="Email" icon="email" />
        </div>
      </div>

      {/* Scroll indicator */}
      <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-700 delay-400 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
        <div className="animate-bounce flex flex-col items-center">
          <span className="text-gray-400 text-sm mb-2">Scroll Down</span>
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </div>
  );
};

const FeatureCard = ({ title, description, link, delay = 0, icon }) => {
  return (
    <Link
      to={link}
      className="block"
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="card card-hover p-5 h-full animate-fadeIn">
        {/* Icon and title */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-md bg-gray-700 flex items-center justify-center mr-3">
            {icon === 'user' && (
              <svg className="w-5 h-5 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )}
            {icon === 'code' && (
              <svg className="w-5 h-5 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
            )}
            {icon === 'projects' && (
              <svg className="w-5 h-5 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            )}
            {icon === 'contact' && (
              <svg className="w-5 h-5 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            )}
          </div>
          <h3 className="text-lg font-medium text-white">{title}</h3>
        </div>

        {/* Description */}
        <p className="text-gray-400 text-sm mb-4">{description}</p>

        {/* View more link */}
        <div className="mt-auto">
          <div className="inline-flex items-center text-indigo-400 text-sm font-medium">
            View More
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </div>
        </div>
      </div>
    </Link>
  );
};

const SocialLink = ({ href, label, icon }) => {
  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="relative group"
      aria-label={label}
    >
      <div className="p-3 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors duration-300">
        {icon === 'github' && (
          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        )}
        {icon === 'linkedin' && (
          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
          </svg>
        )}
        {icon === 'email' && (
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        )}
      </div>

      {/* Simple tooltip */}
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 px-2 py-1 rounded text-xs text-white whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {label}
      </div>
    </a>
  );
};

export default Home;
