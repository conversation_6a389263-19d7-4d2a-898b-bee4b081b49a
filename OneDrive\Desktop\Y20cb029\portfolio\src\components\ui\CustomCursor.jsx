import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const CustomCursor = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [cursorVariant, setCursorVariant] = useState('default');
  const [cursorText, setCursorText] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [cursorColor, setCursorColor] = useState('secondary'); // 'secondary', 'accent', 'tertiary'

  useEffect(() => {
    // Show cursor only after mouse moves
    const handleMouseEnter = () => {
      setIsVisible(true);
    };

    // Hide cursor when mouse leaves the window
    const handleMouseLeave = () => {
      setIsVisible(false);
    };

    // Track mouse position
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    // Track mouse clicks
    const handleMouseDown = () => {
      setIsClicking(true);
    };

    const handleMouseUp = () => {
      setIsClicking(false);
    };

    // Track hover states with enhanced detection
    const handleMouseOver = (e) => {
      const target = e.target;

      // Check for data attributes first
      const cursorType = target.getAttribute('data-cursor-type');
      const cursorTextAttr = target.getAttribute('data-cursor-text');
      const cursorColorAttr = target.getAttribute('data-cursor-color');

      if (cursorType) {
        setCursorVariant(cursorType);
        if (cursorTextAttr) setCursorText(cursorTextAttr);
        if (cursorColorAttr) setCursorColor(cursorColorAttr);
        return;
      }

      // Check for common interactive elements
      if (target.tagName === 'A' ||
          target.tagName === 'BUTTON' ||
          target.closest('a') ||
          target.closest('button') ||
          target.classList.contains('cursor-hover')) {

        // Check for specific classes to determine cursor type
        if (target.classList.contains('btn-primary') ||
            (target.closest('a') && target.closest('a').classList.contains('btn-primary'))) {
          setCursorVariant('hover');
          setCursorText('');
          setCursorColor('secondary');
        } else if (target.classList.contains('btn-secondary') ||
                  (target.closest('a') && target.closest('a').classList.contains('btn-secondary'))) {
          setCursorVariant('hover');
          setCursorText('');
          setCursorColor('accent');
        } else if (target.classList.contains('view-more') ||
                  (target.closest('a') && target.closest('a').classList.contains('view-more'))) {
          setCursorVariant('text');
          setCursorText('View');
          setCursorColor('secondary');
        } else if (target.classList.contains('download') ||
                  (target.closest('a') && target.closest('a').classList.contains('download'))) {
          setCursorVariant('text');
          setCursorText('Download');
          setCursorColor('accent');
        } else {
          setCursorVariant('hover');
          setCursorText('');
          setCursorColor('secondary');
        }
      } else if (target.tagName === 'INPUT' ||
                target.tagName === 'TEXTAREA' ||
                target.classList.contains('cursor-text')) {
        setCursorVariant('text');
        setCursorText('Type');
        setCursorColor('tertiary');
      } else {
        setCursorVariant('default');
        setCursorText('');
        setCursorColor('secondary');
      }
    };

    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseover', handleMouseOver);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseover', handleMouseOver);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  // Get color values based on the current cursor color
  const getColorValues = () => {
    switch (cursorColor) {
      case 'accent':
        return {
          border: 'rgba(138, 77, 255, 0.8)',
          fill: 'rgba(138, 77, 255, 0.1)',
          dot: 'rgba(138, 77, 255, 1)',
          glow: 'rgba(138, 77, 255, 0.5)'
        };
      case 'tertiary':
        return {
          border: 'rgba(255, 94, 98, 0.8)',
          fill: 'rgba(255, 94, 98, 0.1)',
          dot: 'rgba(255, 94, 98, 1)',
          glow: 'rgba(255, 94, 98, 0.5)'
        };
      case 'secondary':
      default:
        return {
          border: 'rgba(10, 240, 255, 0.8)',
          fill: 'rgba(10, 240, 255, 0.1)',
          dot: 'rgba(10, 240, 255, 1)',
          glow: 'rgba(10, 240, 255, 0.5)'
        };
    }
  };

  const colors = getColorValues();

  // Enhanced cursor variants
  const variants = {
    default: {
      x: mousePosition.x - 16,
      y: mousePosition.y - 16,
      height: 32,
      width: 32,
      backgroundColor: 'rgba(10, 240, 255, 0)',
      mixBlendMode: 'normal',
      border: `2px solid ${colors.border}`,
      scale: isClicking ? 0.9 : 1,
    },
    hover: {
      x: mousePosition.x - 24,
      y: mousePosition.y - 24,
      height: 48,
      width: 48,
      backgroundColor: colors.fill,
      mixBlendMode: 'difference',
      border: `2px solid ${colors.border}`,
      scale: isClicking ? 0.9 : 1,
    },
    text: {
      x: mousePosition.x - 40,
      y: mousePosition.y - 40,
      height: 80,
      width: 80,
      backgroundColor: colors.fill,
      mixBlendMode: 'normal',
      border: `2px solid ${colors.border}`,
      scale: isClicking ? 0.9 : 1,
    },
  };

  // Dot variants (inner cursor)
  const dotVariants = {
    default: {
      x: mousePosition.x - 4,
      y: mousePosition.y - 4,
      height: 8,
      width: 8,
      backgroundColor: colors.dot,
      scale: isClicking ? 0.5 : 1,
    },
    hover: {
      x: mousePosition.x - 4,
      y: mousePosition.y - 4,
      height: 8,
      width: 8,
      backgroundColor: colors.dot,
      scale: isClicking ? 0.5 : 1,
    },
    text: {
      x: mousePosition.x - 4,
      y: mousePosition.y - 4,
      height: 8,
      width: 8,
      backgroundColor: colors.dot,
      scale: isClicking ? 0.5 : 1,
    },
  };

  // Glow effect variants
  const glowVariants = {
    default: {
      x: mousePosition.x - 50,
      y: mousePosition.y - 50,
      height: 100,
      width: 100,
      backgroundColor: colors.glow,
      opacity: 0.1,
    },
    hover: {
      x: mousePosition.x - 75,
      y: mousePosition.y - 75,
      height: 150,
      width: 150,
      backgroundColor: colors.glow,
      opacity: 0.15,
    },
    text: {
      x: mousePosition.x - 100,
      y: mousePosition.y - 100,
      height: 200,
      width: 200,
      backgroundColor: colors.glow,
      opacity: 0.1,
    },
  };

  return (
    <>
      {isVisible && (
        <>
          {/* Glow effect */}
          <motion.div
            className="cursor-glow fixed top-0 left-0 rounded-full pointer-events-none z-40 blur-xl"
            variants={glowVariants}
            animate={cursorVariant}
            transition={{
              type: 'spring',
              damping: 30,
              stiffness: 200,
              mass: 0.5,
            }}
          />

          {/* Main cursor ring */}
          <motion.div
            className="cursor-ring fixed top-0 left-0 rounded-full pointer-events-none z-50 backdrop-blur-sm flex items-center justify-center"
            variants={variants}
            animate={cursorVariant}
            transition={{
              type: 'spring',
              damping: 20,
              stiffness: 150,
              mass: 0.8,
            }}
          >
            {cursorVariant === 'text' && cursorText && (
              <motion.span
                className="text-xs font-mono"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                style={{ color: colors.dot }}
              >
                {cursorText}
              </motion.span>
            )}
          </motion.div>

          {/* Dot cursor */}
          <motion.div
            className="cursor-dot fixed top-0 left-0 rounded-full pointer-events-none z-50"
            variants={dotVariants}
            animate={cursorVariant}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
              mass: 0.5,
            }}
          />

          {/* Trailing dots effect */}
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              className="cursor-trail fixed top-0 left-0 rounded-full pointer-events-none z-45"
              initial={{
                x: mousePosition.x - 4,
                y: mousePosition.y - 4,
                height: 8,
                width: 8,
                opacity: 0.5 - (i * 0.1),
                backgroundColor: colors.dot
              }}
              animate={{
                x: mousePosition.x - 4,
                y: mousePosition.y - 4,
                opacity: 0.5 - (i * 0.1)
              }}
              transition={{
                duration: 0.5,
                delay: 0.05 * i
              }}
            />
          ))}
        </>
      )}
    </>
  );
};

export default CustomCursor;
