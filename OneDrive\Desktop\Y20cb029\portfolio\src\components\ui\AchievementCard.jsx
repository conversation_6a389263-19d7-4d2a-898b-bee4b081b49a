import { motion } from 'framer-motion';
import { FiExternalLink, FiCalendar, FiTag } from 'react-icons/fi';

const AchievementCard = ({ 
  item, 
  index, 
  type, 
  onClick,
  className = "" 
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(item);
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'certifications':
        return '🏆';
      case 'internships':
        return '💼';
      case 'achievements':
        return '⭐';
      default:
        return '📄';
    }
  };

  const getTypeColor = () => {
    switch (type) {
      case 'certifications':
        return 'secondary';
      case 'internships':
        return 'accent';
      case 'achievements':
        return 'tertiary';
      default:
        return 'secondary';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -8, scale: 1.02 }}
      className={`group cursor-pointer ${className}`}
      onClick={handleClick}
    >
      <div className="relative bg-dark rounded-xl overflow-hidden border border-light/10 hover:border-secondary/50 transition-all duration-500 hover:shadow-neon h-full">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-cyber-grid bg-cyber-grid opacity-5" />
        
        {/* Image Container */}
        <div className="relative h-48 overflow-hidden">
          <img
            src={item.image}
            alt={item.title}
            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `/placeholder.svg?height=200&width=400&text=${encodeURIComponent(item.title)}`;
            }}
          />
          
          {/* Overlay Gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-dark via-dark/50 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
          
          {/* Type Icon */}
          <div className="absolute top-4 left-4 w-10 h-10 bg-dark/80 backdrop-blur-sm rounded-full flex items-center justify-center text-lg">
            {getTypeIcon()}
          </div>
          
          {/* Year Badge */}
          <div className={`absolute top-4 right-4 bg-${getTypeColor()} text-primary px-3 py-1 rounded-full text-sm font-bold shadow-lg`}>
            {item.year}
          </div>

          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-primary/90 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-4">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileHover={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="flex items-center gap-2 text-secondary font-medium"
            >
              <FiExternalLink className="w-4 h-4" />
              <span className="text-sm">View Details</span>
            </motion.div>
          </div>
        </div>

        {/* Content */}
        <div className="relative p-6 flex flex-col h-full">
          {/* Title */}
          <h3 className="text-lg font-bold text-lightest mb-2 group-hover:text-secondary transition-colors duration-300 line-clamp-2">
            {item.title}
          </h3>
          
          {/* Subtitle with Icon */}
          <div className="flex items-center gap-2 mb-3">
            <FiCalendar className="w-4 h-4 text-light/50" />
            <p className={`text-${getTypeColor()} font-medium text-sm`}>
              {type === 'certifications' && item.issuer}
              {type === 'internships' && `${item.company} • ${item.duration}`}
              {type === 'achievements' && item.event}
            </p>
          </div>
          
          {/* Description */}
          <p className="text-light/70 text-sm mb-4 line-clamp-3 flex-grow">
            {item.description}
          </p>

          {/* Skills/Tags */}
          {item.skills && item.skills.length > 0 && (
            <div className="mt-auto">
              <div className="flex items-center gap-2 mb-2">
                <FiTag className="w-3 h-3 text-light/50" />
                <span className="text-xs text-light/50 font-medium">Skills</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {item.skills.slice(0, 3).map((skill, skillIndex) => (
                  <motion.span
                    key={skillIndex}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: skillIndex * 0.1 }}
                    className={`px-2 py-1 bg-${getTypeColor()}/20 text-${getTypeColor()} text-xs rounded-full font-medium border border-${getTypeColor()}/30 hover:bg-${getTypeColor()}/30 transition-colors duration-200`}
                  >
                    {skill}
                  </motion.span>
                ))}
                {item.skills.length > 3 && (
                  <span className="px-2 py-1 bg-light/10 text-light/60 text-xs rounded-full font-medium border border-light/20">
                    +{item.skills.length - 3}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Category Badge (if applicable) */}
          {item.category && (
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <span className="px-2 py-1 bg-dark/80 backdrop-blur-sm text-light/80 text-xs rounded-full border border-light/20">
                {item.category}
              </span>
            </div>
          )}
        </div>

        {/* Animated Border */}
        <div className={`absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-${getTypeColor()}/50 transition-all duration-300 pointer-events-none`} />
        
        {/* Glow Effect */}
        <div className={`absolute inset-0 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 shadow-neon-${getTypeColor()} pointer-events-none`} />
      </div>
    </motion.div>
  );
};

export default AchievementCard;
