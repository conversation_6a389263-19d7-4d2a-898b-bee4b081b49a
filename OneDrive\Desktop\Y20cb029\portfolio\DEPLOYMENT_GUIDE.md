# Deployment Guide for Your Portfolio Website

This guide will help you deploy your portfolio website to various hosting platforms.

## Prerequisites

Before deploying, make sure to:

1. Replace all placeholder content with your actual information
2. Add your profile photo to `/public/images/profile.jpg`
3. Add your project images to `/public/images/project1.jpg`, etc.
4. Add your resume PDF to `/public/resume.pdf`
5. Test your website locally by running `npm run dev`
6. Build your website by running `npm run build`

## Option 1: Deploy to Netlify

Netlify is a popular platform for hosting static websites with continuous deployment.

### Steps:

1. Create an account on [Netlify](https://www.netlify.com/)
2. Install Git if you haven't already
3. Create a GitHub repository for your portfolio
4. Push your code to GitHub:
   ```
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/yourusername/portfolio.git
   git push -u origin main
   ```
5. In Netlify, click "New site from Git"
6. Choose GitHub and select your repository
7. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
8. Click "Deploy site"

Your site will be deployed to a Netlify URL (e.g., `your-portfolio.netlify.app`). You can later connect a custom domain if desired.

## Option 2: Deploy to Vercel

Vercel is another excellent platform for hosting React applications.

### Steps:

1. Create an account on [Vercel](https://vercel.com/)
2. Install Git if you haven't already
3. Create a GitHub repository for your portfolio
4. Push your code to GitHub (see steps above)
5. In Vercel, click "Import Project"
6. Choose "Import Git Repository" and select your GitHub repository
7. Configure project settings:
   - Framework Preset: Vite
   - Build Command: `npm run build`
   - Output Directory: `dist`
8. Click "Deploy"

Your site will be deployed to a Vercel URL (e.g., `your-portfolio.vercel.app`). You can later connect a custom domain.

## Option 3: Deploy to GitHub Pages

GitHub Pages is a free hosting service provided by GitHub.

### Steps:

1. Install the GitHub Pages dependency:
   ```
   npm install gh-pages --save-dev
   ```

2. Update your `package.json` file:
   ```json
   "homepage": "https://yourusername.github.io/portfolio",
   "scripts": {
     // other scripts...
     "predeploy": "npm run build",
     "deploy": "gh-pages -d dist"
   }
   ```

3. Update your `vite.config.js` file:
   ```javascript
   export default defineConfig({
     plugins: [react()],
     base: '/portfolio/', // Add this line
   })
   ```

4. Create a GitHub repository named `portfolio`
5. Push your code to GitHub (see steps above)
6. Run the deploy command:
   ```
   npm run deploy
   ```

Your site will be deployed to `https://yourusername.github.io/portfolio`.

## Custom Domain (Optional)

For a more professional look, you can connect a custom domain to any of these hosting platforms:

1. Purchase a domain from a registrar like Namecheap, GoDaddy, or Google Domains
2. Follow the instructions on your chosen hosting platform to connect your custom domain
3. Update your DNS settings at your domain registrar as instructed

## Keeping Your Site Updated

After making changes to your portfolio:

1. Test changes locally with `npm run dev`
2. Commit and push changes to GitHub:
   ```
   git add .
   git commit -m "Description of changes"
   git push
   ```
3. If using Netlify or Vercel with continuous deployment, your site will update automatically
4. If using GitHub Pages, run `npm run deploy` again

## Troubleshooting

- If images don't appear, check that paths are correct and case-sensitive
- If styling looks wrong, make sure the build process completed successfully
- If deployment fails, check the error logs on your hosting platform

For more specific issues, consult the documentation of your chosen hosting platform.
