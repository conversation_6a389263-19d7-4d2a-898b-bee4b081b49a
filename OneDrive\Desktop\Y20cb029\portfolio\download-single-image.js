const fs = require('fs');
const path = require('path');
const https = require('https');

// Get command line arguments
const imageName = process.argv[2];
const imageUrl = process.argv[3];

if (!imageName || !imageUrl) {
  console.error('Usage: node download-single-image.js <image-name> <image-url>');
  process.exit(1);
}

// Create the projects directory if it doesn't exist
const projectsDir = path.join(__dirname, 'public', 'images', 'projects');
if (!fs.existsSync(projectsDir)) {
  fs.mkdirSync(projectsDir, { recursive: true });
  console.log(`Created directory: ${projectsDir}`);
}

const imagePath = path.join(projectsDir, imageName);

// Function to download an image
function downloadImage(imageUrl, imagePath) {
  return new Promise((resolve, reject) => {
    console.log(`Downloading ${imageName} from ${imageUrl}...`);
    
    https.get(imageUrl, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download image: Status code ${response.statusCode}`));
        return;
      }

      const fileStream = fs.createWriteStream(imagePath);
      response.pipe(fileStream);

      fileStream.on('finish', () => {
        fileStream.close();
        console.log(`Successfully downloaded ${imageName}`);
        resolve();
      });

      fileStream.on('error', (err) => {
        fs.unlink(imagePath, () => {}); // Delete the file if there was an error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Download the image
downloadImage(imageUrl, imagePath)
  .catch(error => {
    console.error(`Error downloading ${imageName}:`, error.message);
    process.exit(1);
  });
