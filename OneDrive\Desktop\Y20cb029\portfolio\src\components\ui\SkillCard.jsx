import { useState, useRef, useEffect } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';

const SkillCard = ({ 
  skill, 
  category,
  delay = 0,
  onHover = () => {},
  className = '' 
}) => {
  const [hovered, setHovered] = useState(false);
  const cardRef = useRef(null);
  
  // Mouse position for 3D effect
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  
  // Smooth spring physics for mouse movement
  const springConfig = { damping: 25, stiffness: 150 };
  const rotateX = useSpring(useTransform(mouseY, [-0.5, 0.5], [10, -10]), springConfig);
  const rotateY = useSpring(useTransform(mouseX, [-0.5, 0.5], [-10, 10]), springConfig);
  
  // Progress animation
  const [isVisible, setIsVisible] = useState(false);
  const progressRef = useRef(null);
  
  // Observe when card is in viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );
    
    if (cardRef.current) {
      observer.observe(cardRef.current);
    }
    
    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current);
      }
    };
  }, []);
  
  // Handle mouse move for 3D effect
  const handleMouseMove = (e) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Calculate normalized mouse position (-0.5 to 0.5)
    const normalizedX = (e.clientX - centerX) / rect.width;
    const normalizedY = (e.clientY - centerY) / rect.height;
    
    mouseX.set(normalizedX);
    mouseY.set(normalizedY);
  };
  
  // Handle mouse enter/leave
  const handleMouseEnter = () => {
    setHovered(true);
    onHover(true, skill);
  };
  
  const handleMouseLeave = () => {
    setHovered(false);
    mouseX.set(0);
    mouseY.set(0);
    onHover(false, null);
  };
  
  return (
    <motion.div
      ref={cardRef}
      className={`relative ${className}`}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        type: "spring", 
        stiffness: 100, 
        damping: 15,
        delay: delay * 0.1
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        perspective: "1000px",
        transformStyle: "preserve-3d"
      }}
      data-cursor-type={hovered ? "text" : "default"}
      data-cursor-text={hovered ? skill.name : ""}
      data-cursor-color={category.color}
    >
      <motion.div
        className={`bg-dark/80 backdrop-blur-sm p-6 rounded-lg border border-${category.color}/20 h-full`}
        style={{
          transformStyle: "preserve-3d",
          rotateX: rotateX,
          rotateY: rotateY,
          z: 0
        }}
      >
        {/* Glow effect on hover */}
        <motion.div 
          className={`absolute inset-0 rounded-lg bg-${category.color}/5 opacity-0 blur-md`}
          animate={{ opacity: hovered ? 0.8 : 0 }}
          transition={{ duration: 0.3 }}
        />
        
        {/* Card content with 3D layers */}
        <div className="relative z-10">
          <div className="flex items-center mb-4">
            {/* Icon with 3D effect */}
            <motion.div 
              className="text-3xl mr-3"
              style={{ 
                transformStyle: "preserve-3d",
                z: 20,
                rotateY: hovered ? 180 : 0
              }}
              transition={{ duration: 0.5 }}
            >
              {skill.icon}
            </motion.div>
            
            {/* Skill name */}
            <motion.h4 
              className="text-lg font-medium text-lightest"
              style={{ z: 10 }}
              animate={{ 
                color: hovered ? `var(--color-${category.color})` : "#f8fafc"
              }}
              transition={{ duration: 0.3 }}
            >
              {skill.name}
            </motion.h4>
          </div>
          
          {/* Skill level bar with animated fill and glow effect */}
          <div className="relative w-full bg-primary/50 rounded-full h-2.5 mb-2 overflow-hidden" ref={progressRef}>
            <motion.div
              className={`absolute top-0 left-0 h-full bg-${category.color} rounded-full`}
              initial={{ width: 0 }}
              animate={{ width: isVisible ? `${skill.level}%` : 0 }}
              transition={{ 
                duration: 1.5, 
                delay: 0.2 + (delay * 0.1),
                ease: "easeOut" 
              }}
            />
            
            {/* Animated glow effect */}
            <motion.div
              className={`absolute top-0 left-0 h-full w-10 bg-${category.color}/80 rounded-full blur-sm`}
              initial={{ x: -20 }}
              animate={{ 
                x: isVisible ? "100%" : "-10%",
                opacity: hovered ? 1 : 0.5
              }}
              transition={{ 
                duration: 2,
                delay: 1 + (delay * 0.1),
                ease: "easeInOut",
                repeat: Infinity,
                repeatDelay: 3
              }}
            />
          </div>
          
          <div className="flex justify-between text-xs mb-3">
            <span className="text-light/70">Proficiency</span>
            <motion.span 
              className={`text-${category.color}`}
              animate={{ 
                scale: hovered ? [1, 1.2, 1] : 1,
                opacity: hovered ? 1 : 0.8
              }}
              transition={{ duration: 0.5 }}
            >
              {skill.level}%
            </motion.span>
          </div>
          
          {/* Skill description */}
          <motion.p 
            className="text-light/60 text-sm mt-3 border-t border-light/10 pt-3"
            animate={{ opacity: hovered ? 1 : 0.6 }}
            transition={{ duration: 0.3 }}
            style={{ z: 5 }}
          >
            {skill.description}
          </motion.p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default SkillCard;
