import { useInView } from 'framer-motion';
import { useRef } from 'react';

// Custom hook for advanced staggered animations
export const useStaggeredAnimation = (staggerDelay = 0.1) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { 
        type: "spring",
        damping: 20,
        stiffness: 100
      }
    }
  };

  return { ref, isInView, containerVariants, itemVariants };
};

// Custom hook for 3D tilt effect
export const useTiltEffect = () => {
  const handleTilt = (e, intensity = 15) => {
    const card = e.currentTarget;
    const cardRect = card.getBoundingClientRect();
    const cardCenterX = cardRect.left + cardRect.width / 2;
    const cardCenterY = cardRect.top + cardRect.height / 2;
    
    const mouseX = e.clientX;
    const mouseY = e.clientY;
    
    // Calculate rotation based on mouse position relative to card center
    const rotateY = ((mouseX - cardCenterX) / (cardRect.width / 2)) * intensity;
    const rotateX = -((mouseY - cardCenterY) / (cardRect.height / 2)) * intensity;
    
    // Apply the transform
    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`;
    card.style.transition = 'transform 0.1s ease';
  };
  
  const handleTiltExit = (e) => {
    const card = e.currentTarget;
    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)';
    card.style.transition = 'transform 0.5s ease';
  };
  
  return { handleTilt, handleTiltExit };
};

// Custom hook for text reveal animation
export const useTextRevealAnimation = () => {
  const textVariants = {
    hidden: { opacity: 0 },
    visible: (i = 0) => ({
      opacity: 1,
      transition: {
        staggerChildren: 0.03,
        delayChildren: i * 0.1
      }
    })
  };

  const letterVariants = {
    hidden: { 
      opacity: 0,
      y: 20,
      rotateX: 90
    },
    visible: {
      opacity: 1,
      y: 0,
      rotateX: 0,
      transition: { 
        type: "spring",
        damping: 10,
        stiffness: 100
      }
    }
  };

  return { textVariants, letterVariants };
};

// Custom hook for parallax scrolling effect
export const useParallaxEffect = (speed = 0.5) => {
  const ref = useRef(null);
  
  const handleScroll = () => {
    if (!ref.current) return;
    
    const scrollY = window.scrollY;
    const element = ref.current;
    const elementTop = element.getBoundingClientRect().top + scrollY;
    const offset = (scrollY - elementTop) * speed;
    
    element.style.transform = `translateY(${offset}px)`;
  };
  
  return { ref, handleScroll };
};

// Custom hook for magnetic effect
export const useMagneticEffect = (strength = 0.3) => {
  const ref = useRef(null);
  
  const handleMouseMove = (e) => {
    if (!ref.current) return;
    
    const element = ref.current;
    const rect = element.getBoundingClientRect();
    
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const distanceX = e.clientX - centerX;
    const distanceY = e.clientY - centerY;
    
    element.style.transform = `translate(${distanceX * strength}px, ${distanceY * strength}px)`;
  };
  
  const handleMouseLeave = () => {
    if (!ref.current) return;
    
    const element = ref.current;
    element.style.transform = 'translate(0, 0)';
    element.style.transition = 'transform 0.5s ease';
  };
  
  return { ref, handleMouseMove, handleMouseLeave };
};
