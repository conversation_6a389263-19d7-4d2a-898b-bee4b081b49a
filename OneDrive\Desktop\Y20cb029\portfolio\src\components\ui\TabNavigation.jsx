// components/ui/TabNavigation.jsx
import { motion } from 'framer-motion';

const TabNavigation = ({ tabs, activeTab, onTabChange, variant = "default", className = "" }) => {
  return (
    <div className={`flex justify-center ${className}`}>
      <div className="flex bg-dark/50 rounded-lg p-1 border border-light/10">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeTab === tab.id;
          
          return (
            <motion.button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center px-6 py-3 rounded-lg transition-all duration-300 ${
                isActive 
                  ? 'bg-secondary text-primary' 
                  : 'text-light hover:text-secondary'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Icon className="mr-2" />
              {tab.label}
              <span className="ml-2 text-xs bg-light/20 px-2 py-1 rounded-full">
                {tab.count}
              </span>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
};

export default TabNavigation;