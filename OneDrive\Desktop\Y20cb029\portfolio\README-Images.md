# Image Directory Structure

Create the following directory structure in your `public` folder:

\```
public/
├── images/
│   ├── certifications/
│   │   ├── deep-learning.jpg
│   │   ├── aws-cloud.jpg
│   │   ├── dsa-nptel.jpg
│   │   ├── azure-fundamentals.jpg
│   │   └── google-analytics.jpg
│   ├── internships/
│   │   ├── techsolutions.jpg
│   │   ├── dataviz.jpg
│   │   ├── cloudtech.jpg
│   │   └── webcraft.jpg
│   ├── achievements/
│   │   ├── innovation-award.jpg
│   │   ├── deans-list.jpg
│   │   ├── open-source.jpg
│   │   └── coding-contest.jpg
│   └── placeholders/
│       ├── placeholder-certifications.jpg
│       ├── placeholder-internships.jpg
│       └── placeholder-achievements.jpg
\```

## Image Requirements:
- **Format**: JPG or PNG
- **Size**: 800x600px (4:3 aspect ratio) recommended
- **Quality**: High resolution for crisp display
- **File Size**: Optimized for web (under 500KB each)

## Suggested Image Sources:
1. **Unsplash** (https://unsplash.com) - Free high-quality images
2. **Pexels** (https://pexels.com) - Free stock photos
3. **Your own screenshots** - For actual certificates and achievements
4. **Company logos** - For internship companies (ensure proper licensing)

## Placeholder Images:
If you don't have specific images, you can use generic tech-related images or create simple colored rectangles with text overlays.
