import { useState, useRef } from 'react';
import { motion } from 'framer-motion';

const Card3D = ({ 
  children, 
  className = '', 
  glowColor = 'rgba(10, 240, 255, 0.5)',
  intensity = 15,
  glowIntensity = 1.5,
  borderGlow = true,
  ...props 
}) => {
  const [rotateX, setRotateX] = useState(0);
  const [rotateY, setRotateY] = useState(0);
  const [scale, setScale] = useState(1);
  const [glowPosition, setGlowPosition] = useState({ x: 50, y: 50 });
  
  const cardRef = useRef(null);
  
  const handleMouseMove = (e) => {
    if (!cardRef.current) return;
    
    const card = cardRef.current;
    const cardRect = card.getBoundingClientRect();
    
    // Calculate the center of the card
    const cardCenterX = cardRect.left + cardRect.width / 2;
    const cardCenterY = cardRect.top + cardRect.height / 2;
    
    // Calculate the mouse position relative to the center of the card
    const mouseX = e.clientX - cardCenterX;
    const mouseY = e.clientY - cardCenterY;
    
    // Calculate rotation based on mouse position
    const rotateY = (mouseX / (cardRect.width / 2)) * intensity;
    const rotateX = -((mouseY / (cardRect.height / 2)) * intensity);
    
    // Calculate glow position (0-100%)
    const glowX = ((e.clientX - cardRect.left) / cardRect.width) * 100;
    const glowY = ((e.clientY - cardRect.top) / cardRect.height) * 100;
    
    setRotateX(rotateX);
    setRotateY(rotateY);
    setScale(1.02);
    setGlowPosition({ x: glowX, y: glowY });
  };
  
  const handleMouseLeave = () => {
    setRotateX(0);
    setRotateY(0);
    setScale(1);
    setGlowPosition({ x: 50, y: 50 });
  };
  
  return (
    <motion.div
      ref={cardRef}
      className={`relative overflow-hidden ${className}`}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      style={{
        transformStyle: 'preserve-3d',
        transform: `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${scale})`,
        transition: 'transform 0.1s ease',
      }}
      {...props}
    >
      {/* Glow effect */}
      <div 
        className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        style={{
          background: `radial-gradient(circle at ${glowPosition.x}% ${glowPosition.y}%, ${glowColor}, transparent 70%)`,
          opacity: scale > 1 ? glowIntensity * 0.7 : 0,
        }}
      />
      
      {/* Border glow */}
      {borderGlow && (
        <div 
          className="absolute inset-0 pointer-events-none rounded-lg"
          style={{
            boxShadow: `0 0 15px ${glowColor}`,
            opacity: scale > 1 ? glowIntensity * 0.5 : 0,
            transition: 'opacity 0.3s ease',
          }}
        />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
};

export default Card3D;
