<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Image Downloader</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .image-container {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>Project Image Downloader</h1>
    
    <div class="instructions">
        <h2>Instructions:</h2>
        <ol>
            <li>Click the "Download" button below each image</li>
            <li>Save the image to: <code>public/images/projects/</code> with the filename shown</li>
            <li>Make sure to save all images to complete your portfolio</li>
        </ol>
    </div>

    <!-- Web Development Image -->
    <div class="image-container">
        <h3>Web Development (placeholder-web.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Web Development">
        <a href="https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="placeholder-web.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Mobile Development Image -->
    <div class="image-container">
        <h3>Mobile Development (placeholder-mobile.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Mobile Development">
        <a href="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="placeholder-mobile.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- AI & Machine Learning Image -->
    <div class="image-container">
        <h3>AI & Machine Learning (placeholder-ai.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1677442135968-6d89469c6409?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="AI & Machine Learning">
        <a href="https://images.unsplash.com/photo-1677442135968-6d89469c6409?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="placeholder-ai.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Cloud Computing Image -->
    <div class="image-container">
        <h3>Cloud Computing (placeholder-cloud.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Cloud Computing">
        <a href="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="placeholder-cloud.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Software Development Image -->
    <div class="image-container">
        <h3>Software Development (placeholder-software.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Software Development">
        <a href="https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="placeholder-software.jpg">
            <button>Download</button>
        </a>
    </div>

    <!-- Project-specific images -->
    <h2>Project-Specific Images</h2>

    <div class="image-container">
        <h3>Sports Portfolio (sports-portfolio.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Sports Portfolio">
        <a href="https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="sports-portfolio.jpg">
            <button>Download</button>
        </a>
    </div>

    <div class="image-container">
        <h3>Portfolio Showcase (portfolio-showcase.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1545239351-ef35f43d514b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Portfolio Showcase">
        <a href="https://images.unsplash.com/photo-1545239351-ef35f43d514b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="portfolio-showcase.jpg">
            <button>Download</button>
        </a>
    </div>

    <div class="image-container">
        <h3>Chat Checkpoints (chat-checkpoints.jpg)</h3>
        <img src="https://images.unsplash.com/photo-1611606063065-ee7946f0787a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Chat Checkpoints">
        <a href="https://images.unsplash.com/photo-1611606063065-ee7946f0787a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" download="chat-checkpoints.jpg">
            <button>Download</button>
        </a>
    </div>
</body>
</html>
