import os
import sys
import fitz  # PyMuPDF
from PIL import Image
import io

def convert_pdf_to_jpg(pdf_path, output_folder):
    """
    Convert a PDF file to JPG images using PyMuPDF (fitz)
    :param pdf_path: Path to the PDF file
    :param output_folder: Folder to save the JPG images
    """
    try:
        # Get the filename without extension
        filename = os.path.splitext(os.path.basename(pdf_path))[0]

        # Open the PDF file
        pdf_document = fitz.open(pdf_path)

        # Get the first page
        page = pdf_document.load_page(0)

        # Set the rendering resolution (higher for better quality)
        zoom = 4.0  # Adjust as needed for quality
        mat = fitz.Matrix(zoom, zoom)

        # Render the page to a pixmap
        pix = page.get_pixmap(matrix=mat)

        # Convert pixmap to PIL Image
        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

        # Save the image as JPG
        output_path = os.path.join(output_folder, f"{filename}.jpg")
        img.save(output_path, 'JPEG', quality=95)  # High quality

        # Close the PDF document
        pdf_document.close()

        print(f"Converted {pdf_path} to {output_path}")
        return output_path
    except Exception as e:
        print(f"Error converting {pdf_path}: {e}")
        return None

def main():
    # Get the certificates folder path
    certificates_folder = os.path.join("public", "certificates")

    # Create output folder if it doesn't exist
    os.makedirs(certificates_folder, exist_ok=True)

    # Get all PDF files in the certificates folder
    pdf_files = [f for f in os.listdir(certificates_folder) if f.endswith('.pdf')]

    # Convert each PDF to JPG
    for pdf_file in pdf_files:
        pdf_path = os.path.join(certificates_folder, pdf_file)
        convert_pdf_to_jpg(pdf_path, certificates_folder)

if __name__ == "__main__":
    main()
