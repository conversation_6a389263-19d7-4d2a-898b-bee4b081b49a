import { useState } from 'react';
import { Link } from 'react-router-dom';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <nav className="backdrop-blur-md bg-gray-900/80 border-b border-gray-800 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-3">
          {/* Logo */}
          <Link to="/" className="relative group">
            <div className="relative px-3 py-2 rounded-md leading-none flex items-center transition-all duration-300 hover:bg-gray-800/50">
              <span className="font-bold text-xl">
                <span className="text-indigo-400">&lt;</span>
                <span className="text-white">KC</span>
                <span className="text-indigo-400">/&gt;</span>
              </span>
            </div>
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex space-x-1">
            <NavLink to="/">Home</NavLink>
            <NavLink to="/about">About</NavLink>
            <NavLink to="/skills">Skills</NavLink>
            <NavLink to="/projects">Projects</NavLink>
            <NavLink to="/experience">Experience</NavLink>
            <NavLink to="/achievements">Achievements</NavLink>
            <NavLink to="/education">Education</NavLink>
            <NavLink to="/certifications">Certifications</NavLink>
            <NavLink to="/contact">Contact</NavLink>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="p-2 rounded-md focus:outline-none bg-gray-800 hover:bg-gray-700 transition-colors duration-300"
              aria-label="Toggle menu"
            >
              <svg
                className="h-6 w-6 text-gray-200"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isOpen && (
          <div className="md:hidden py-3 border-t border-gray-800 animate-fadeIn">
            <div className="card p-3 flex flex-col space-y-2">
              <MobileNavLink to="/" onClick={toggleMenu}>Home</MobileNavLink>
              <MobileNavLink to="/about" onClick={toggleMenu}>About</MobileNavLink>
              <MobileNavLink to="/skills" onClick={toggleMenu}>Skills</MobileNavLink>
              <MobileNavLink to="/projects" onClick={toggleMenu}>Projects</MobileNavLink>
              <MobileNavLink to="/experience" onClick={toggleMenu}>Experience</MobileNavLink>
              <MobileNavLink to="/achievements" onClick={toggleMenu}>Achievements</MobileNavLink>
              <MobileNavLink to="/education" onClick={toggleMenu}>Education</MobileNavLink>
              <MobileNavLink to="/certifications" onClick={toggleMenu}>Certifications</MobileNavLink>
              <MobileNavLink to="/contact" onClick={toggleMenu}>Contact</MobileNavLink>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

// Desktop Navigation Link
const NavLink = ({ to, children }) => (
  <Link
    to={to}
    className="relative px-3 py-2 group"
  >
    <span className="relative z-10 text-gray-300 group-hover:text-white transition duration-300 font-medium text-sm">
      {children}
    </span>
    <span className="absolute inset-0 rounded-md scale-0 group-hover:scale-100 transition-transform duration-300 bg-gray-800/70"></span>
    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
  </Link>
);

// Mobile Navigation Link
const MobileNavLink = ({ to, children, onClick }) => (
  <Link
    to={to}
    className="px-4 py-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors duration-300 text-sm font-medium"
    onClick={onClick}
  >
    {children}
  </Link>
);

export default Navbar;
