import { useEffect, useRef, useState } from 'react';

const ParticleBackground = ({
  count = 100,
  color = '#0af0ff',
  secondaryColor = '#8a4dff',
  tertiaryColor = '#ff5e62',
  speed = 1,
  size = 2,
  opacity = 0.5,
  linkOpacity = 0.2,
  linkDistance = 150,
  interactive = true,
  className = ''
}) => {
  const canvasRef = useRef(null);
  const [mousePosition, setMousePosition] = useState({ x: null, y: null });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let particles = [];
    let hoverParticle = null;

    // Set canvas size
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;

      // Recreate particles when resizing
      initParticles();
    };

    // Mouse interaction
    const handleMouseMove = (e) => {
      if (!interactive) return;

      const rect = canvas.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });

      // Create hover particle if it doesn't exist
      if (!hoverParticle) {
        hoverParticle = new Particle(e.clientX - rect.left, e.clientY - rect.top, true);
        hoverParticle.size = size * 1.5;
        hoverParticle.opacity = 0.8;
      } else {
        // Update hover particle position
        hoverParticle.x = e.clientX - rect.left;
        hoverParticle.y = e.clientY - rect.top;
      }

      setIsHovering(true);
    };

    const handleMouseLeave = () => {
      setIsHovering(false);
      hoverParticle = null;
    };

    window.addEventListener('resize', handleResize);
    if (interactive) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseleave', handleMouseLeave);
    }

    handleResize();

    // Particle class with enhanced features
    class Particle {
      constructor(x, y, isHover = false) {
        this.x = x;
        this.y = y;
        this.size = Math.random() * size + 0.5;
        this.baseSize = this.size;
        this.speedX = (Math.random() - 0.5) * speed;
        this.speedY = (Math.random() - 0.5) * speed;

        // Random color from the three options
        const colorRand = Math.random();
        if (colorRand < 0.33) {
          this.color = color;
        } else if (colorRand < 0.66) {
          this.color = secondaryColor;
        } else {
          this.color = tertiaryColor;
        }

        this.opacity = Math.random() * opacity;
        this.isHover = isHover;

        // For wave effect
        this.angle = Math.random() * Math.PI * 2;
        this.angleSpeed = 0.01 + Math.random() * 0.02;
        this.waveAmplitude = 0.5 + Math.random() * 1;

        // For pulse effect
        this.pulsePhase = Math.random() * Math.PI * 2;
        this.pulseSpeed = 0.02 + Math.random() * 0.02;
      }

      update() {
        if (!this.isHover) {
          this.x += this.speedX;
          this.y += this.speedY;

          // Add slight wave motion
          this.x += Math.sin(this.angle) * this.waveAmplitude * 0.1;
          this.y += Math.cos(this.angle) * this.waveAmplitude * 0.1;
          this.angle += this.angleSpeed;

          // Pulse size effect
          this.size = this.baseSize + Math.sin(this.pulsePhase) * 0.5;
          this.pulsePhase += this.pulseSpeed;

          // Bounce off edges with slight randomization
          if (this.x > canvas.width || this.x < 0) {
            this.speedX = -this.speedX * (0.9 + Math.random() * 0.2);
            if (Math.random() > 0.9) this.speedY *= (0.9 + Math.random() * 0.2); // Occasional y-speed change on x-bounce
          }

          if (this.y > canvas.height || this.y < 0) {
            this.speedY = -this.speedY * (0.9 + Math.random() * 0.2);
            if (Math.random() > 0.9) this.speedX *= (0.9 + Math.random() * 0.2); // Occasional x-speed change on y-bounce
          }

          // Mouse interaction - particles are attracted to mouse position
          if (isHovering && mousePosition.x && mousePosition.y) {
            const dx = mousePosition.x - this.x;
            const dy = mousePosition.y - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 150) {
              const forceDirectionX = dx / distance;
              const forceDirectionY = dy / distance;
              const force = (150 - distance) / 150;

              this.speedX += forceDirectionX * force * 0.2;
              this.speedY += forceDirectionY * force * 0.2;

              // Limit speed
              const maxSpeed = 3;
              const currentSpeed = Math.sqrt(this.speedX * this.speedX + this.speedY * this.speedY);
              if (currentSpeed > maxSpeed) {
                this.speedX = (this.speedX / currentSpeed) * maxSpeed;
                this.speedY = (this.speedY / currentSpeed) * maxSpeed;
              }
            }
          }
        }
      }

      draw() {
        // Glow effect
        const glow = this.isHover ? 10 : 5;

        // Draw glow
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size + glow, 0, Math.PI * 2);
        const gradient = ctx.createRadialGradient(
          this.x, this.y, this.size,
          this.x, this.y, this.size + glow
        );
        gradient.addColorStop(0, this.color);
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
        ctx.fillStyle = gradient;
        ctx.globalAlpha = this.opacity * 0.3;
        ctx.fill();

        // Draw particle
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.globalAlpha = this.opacity;
        ctx.fill();
        ctx.globalAlpha = 1;
      }
    }

    // Initialize particles
    const initParticles = () => {
      particles = [];
      for (let i = 0; i < count; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        particles.push(new Particle(x, y));
      }
    };

    // Connect particles with lines if they're close enough
    const connectParticles = () => {
      // Add hover particle to connections if it exists
      const allParticles = hoverParticle ? [...particles, hoverParticle] : particles;

      for (let i = 0; i < allParticles.length; i++) {
        for (let j = i + 1; j < allParticles.length; j++) {
          const dx = allParticles[i].x - allParticles[j].x;
          const dy = allParticles[i].y - allParticles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          // Adjust link distance for hover particle
          const effectiveLinkDistance =
            (allParticles[i].isHover || allParticles[j].isHover)
              ? linkDistance * 1.5
              : linkDistance;

          if (distance < effectiveLinkDistance) {
            // Calculate opacity based on distance
            const opacity = 1 - (distance / effectiveLinkDistance);

            // Create gradient for line
            const gradient = ctx.createLinearGradient(
              allParticles[i].x, allParticles[i].y,
              allParticles[j].x, allParticles[j].y
            );
            gradient.addColorStop(0, allParticles[i].color);
            gradient.addColorStop(1, allParticles[j].color);

            ctx.beginPath();
            ctx.strokeStyle = gradient;

            // Increase opacity for hover particle connections
            const effectiveOpacity =
              (allParticles[i].isHover || allParticles[j].isHover)
                ? opacity * linkOpacity * 2
                : opacity * linkOpacity;

            ctx.globalAlpha = effectiveOpacity;
            ctx.lineWidth = (allParticles[i].isHover || allParticles[j].isHover) ? 1 : 0.5;
            ctx.moveTo(allParticles[i].x, allParticles[i].y);
            ctx.lineTo(allParticles[j].x, allParticles[j].y);
            ctx.stroke();
            ctx.globalAlpha = 1;
          }
        }
      }
    };

    // Animation loop with improved rendering
    const animate = () => {
      // Clear with slight fade effect for motion trails
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particles.forEach(particle => {
        particle.update();
        particle.draw();
      });

      // Draw hover particle if it exists
      if (hoverParticle) {
        hoverParticle.draw();
      }

      connectParticles();

      animationFrameId = requestAnimationFrame(animate);
    };

    initParticles();
    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      if (interactive) {
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseleave', handleMouseLeave);
      }
      cancelAnimationFrame(animationFrameId);
    };
  }, [count, color, secondaryColor, tertiaryColor, speed, size, opacity, linkOpacity, linkDistance, interactive, isHovering, mousePosition]);

  return (
    <canvas
      ref={canvasRef}
      className={`fixed top-0 left-0 w-full h-full ${interactive ? 'cursor-none' : 'pointer-events-none'} z-0 ${className}`}
    />
  );
};

export default ParticleBackground;
