const Certifications = () => {
  // Comprehensive certifications data
  const certifications = [
    {
      title: "Deep Learning Specialization",
      issuer: "DeepLearning.AI (Coursera)",
      date: "April 2023",
      credentialId: "DL-SPEC-2023",
      credentialURL: "https://www.coursera.org/account/accomplishments/specialization/DL-SPEC-2023",
      skills: ["Neural Networks", "CNN", "RNN", "LSTM", "Transformers", "Computer Vision", "NLP"],
      description: "Five-course specialization by <PERSON> covering neural networks, deep learning architectures, and their applications in computer vision and natural language processing."
    },
    {
      title: "Machine Learning Specialization",
      issuer: "Stanford University & DeepLearning.AI (Coursera)",
      date: "January 2023",
      credentialId: "ML-SPEC-2023",
      credentialURL: "https://www.coursera.org/account/accomplishments/specialization/ML-SPEC-2023",
      skills: ["Machine Learning", "Supervised Learning", "Unsupervised Learning", "Recommender Systems", "Reinforcement Learning"],
      description: "Three-course specialization covering machine learning algorithms, neural networks, and practical applications with hands-on projects."
    },
    {
      title: "TensorFlow Developer Professional Certificate",
      issuer: "DeepLearning.AI (Coursera)",
      date: "March 2023",
      credentialId: "TF-DEV-2023",
      credentialURL: "https://www.coursera.org/account/accomplishments/professional-cert/TF-DEV-2023",
      skills: ["TensorFlow", "Keras", "Deep Learning", "Computer Vision", "NLP", "Model Deployment"],
      description: "Professional certification demonstrating proficiency in building and deploying machine learning models with TensorFlow, including computer vision and NLP applications."
    },
    {
      title: "AWS Certified Machine Learning - Specialty",
      issuer: "Amazon Web Services",
      date: "February 2023",
      credentialId: "AWS-ML-2023",
      credentialURL: "https://aws.amazon.com/verification",
      skills: ["AWS SageMaker", "AWS Lambda", "ML Pipelines", "Cloud ML Infrastructure", "Model Deployment"],
      description: "Advanced certification validating expertise in designing, implementing, and maintaining machine learning solutions on AWS."
    },
    {
      title: "Data Science Professional Certificate",
      issuer: "IBM (Coursera)",
      date: "December 2022",
      credentialId: "IBM-DS-2022",
      credentialURL: "https://www.coursera.org/account/accomplishments/professional-cert/IBM-DS-2022",
      skills: ["Data Science", "Python", "SQL", "Data Analysis", "Data Visualization", "Machine Learning"],
      description: "Ten-course professional certificate covering the entire data science process from data collection to machine learning model deployment."
    },
    {
      title: "Full Stack Web Development Bootcamp",
      issuer: "Udemy",
      date: "November 2022",
      credentialId: "UC-FSWD-2022",
      credentialURL: "https://www.udemy.com/certificate/UC-FSWD-2022/",
      skills: ["React", "Node.js", "Express", "MongoDB", "RESTful APIs", "Authentication", "Redux"],
      description: "Comprehensive bootcamp covering frontend and backend development with modern JavaScript frameworks and tools, including real-world projects."
    },
    {
      title: "Python for Data Science and Machine Learning",
      issuer: "Coursera",
      date: "August 2022",
      credentialId: "PY-DS-ML-2022",
      credentialURL: "https://www.coursera.org/verify/PY-DS-ML-2022",
      skills: ["Python", "NumPy", "Pandas", "Matplotlib", "Scikit-learn", "Seaborn", "Jupyter"],
      description: "In-depth course on using Python for data analysis, visualization, and building machine learning models with practical applications."
    },
    {
      title: "AWS Certified Cloud Practitioner",
      issuer: "Amazon Web Services",
      date: "October 2022",
      credentialId: "AWS-CCP-2022",
      credentialURL: "https://aws.amazon.com/verification",
      skills: ["AWS Services", "Cloud Architecture", "Security", "Pricing Models", "Cloud Concepts"],
      description: "Foundational certification validating understanding of AWS Cloud services, architecture, security, and pricing models."
    },
    {
      title: "Google Data Analytics Professional Certificate",
      issuer: "Google (Coursera)",
      date: "July 2022",
      credentialId: "GDAPC-2022",
      credentialURL: "https://www.coursera.org/account/accomplishments/professional-cert/GDAPC-2022",
      skills: ["Data Analysis", "SQL", "R Programming", "Tableau", "Data Cleaning", "Data Visualization"],
      description: "Eight-course program covering the complete data analysis process from asking questions to presenting insights, using industry-standard tools."
    },
    {
      title: "UI/UX Design Professional Certificate",
      issuer: "Google (Coursera)",
      date: "May 2022",
      credentialId: "UXDC-2022",
      credentialURL: "https://www.coursera.org/account/accomplishments/professional-cert/UXDC-2022",
      skills: ["User Research", "Wireframing", "Prototyping", "Usability Testing", "Figma", "Adobe XD"],
      description: "Comprehensive program covering principles of user interface design, user experience, and creating effective digital products with industry tools."
    },
    {
      title: "JavaScript Algorithms and Data Structures",
      issuer: "freeCodeCamp",
      date: "March 2022",
      credentialId: "FCC-JADS-2022",
      credentialURL: "https://freecodecamp.org/certification/user/javascript-algorithms-and-data-structures",
      skills: ["JavaScript", "ES6", "Algorithms", "Data Structures", "Problem Solving", "Functional Programming"],
      description: "Certification covering JavaScript fundamentals, algorithms, and data structures with practical challenges and projects."
    },
    {
      title: "Responsive Web Design",
      issuer: "freeCodeCamp",
      date: "January 2022",
      credentialId: "FCC-RWD-2022",
      credentialURL: "https://freecodecamp.org/certification/user/responsive-web-design",
      skills: ["HTML5", "CSS3", "Flexbox", "CSS Grid", "Responsive Design", "Accessibility"],
      description: "Certification covering modern responsive web design techniques and principles with hands-on projects."
    },
    {
      title: "Microsoft Azure Fundamentals (AZ-900)",
      issuer: "Microsoft",
      date: "December 2021",
      credentialId: "MS-AZ900-2021",
      credentialURL: "https://www.microsoft.com/en-us/learning/certification-dashboard.aspx",
      skills: ["Azure Services", "Cloud Concepts", "Security", "Pricing", "Support"],
      description: "Foundational certification validating knowledge of cloud concepts, Azure services, security, privacy, pricing, and support."
    },
    {
      title: "SQL for Data Science",
      issuer: "University of California, Davis (Coursera)",
      date: "October 2021",
      credentialId: "SQL-DS-2021",
      credentialURL: "https://www.coursera.org/verify/SQL-DS-2021",
      skills: ["SQL", "Database Design", "Data Manipulation", "Query Optimization", "Data Analysis"],
      description: "Course covering SQL fundamentals and advanced techniques for data analysis and manipulation in data science contexts."
    }
  ];

  return (
    <div className="py-10">
      <h1 className="text-4xl font-bold mb-8 text-center">
        <span className="text-blue-400">My</span> Certifications
      </h1>

      <p className="text-gray-300 text-center max-w-3xl mx-auto mb-12 leading-relaxed">
        I'm committed to continuous learning and professional development. Here are some of the certifications
        I've earned to enhance my skills and knowledge in various technologies.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {certifications.map((cert, index) => (
          <CertificationCard key={index} certification={cert} />
        ))}
      </div>
    </div>
  );
};

const CertificationCard = ({ certification }) => {
  return (
    <div className="glass-card-dark p-6 rounded-lg shadow-lg hover-lift border border-blue-700/30 hover:border-blue-500/50 transition-all duration-500">
      <div className="flex justify-between items-start mb-4">
        <h2 className="text-xl font-bold text-gradient-alt animate-neon">{certification.title}</h2>
        <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-xs text-white px-3 py-1 rounded-full shadow-glow">
          {certification.date}
        </span>
      </div>

      <h3 className="text-lg text-blue-300 mb-3">{certification.issuer}</h3>

      <p className="text-gray-300 mb-4 leading-relaxed">
        {certification.description}
      </p>

      <div className="mb-4 bg-blue-900/30 p-3 rounded-lg border border-blue-800/50">
        <h4 className="text-sm font-semibold text-gradient mb-2">Credential ID:</h4>
        <p className="text-gray-300 text-sm font-mono">{certification.credentialId}</p>
      </div>

      <div className="mb-6">
        <h4 className="text-sm font-semibold text-gradient mb-2">Skills:</h4>
        <div className="flex flex-wrap gap-2">
          {certification.skills.map((skill, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-blue-800/40 backdrop-blur-sm text-xs rounded-full text-blue-200 border border-blue-700/30 hover:border-blue-500/50 transition-all duration-300"
            >
              {skill}
            </span>
          ))}
        </div>
      </div>

      <a
        href={certification.credentialURL}
        target="_blank"
        rel="noopener noreferrer"
        className="cyber-button inline-flex items-center"
      >
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
        Verify Credential
      </a>
    </div>
  );
};

export default Certifications;
